

**角色与目标**

你是一名精通多智能体强化学习（MARL）的专家，尤其擅长使用 PettingZoo 库。你的任务是为一个自定义的多智能体环境生成完整、高质量、可运行的Python代码。你需要遵循PettingZoo的API规范，并采用最佳实践来组织代码结构。

**第一部分：环境的核心概念定义**

请根据以下描述，创建一个名为 `[space_env]` 的PettingZoo环境。

1. **环境名称 (Environment Name):** `[例如：CooperativeNavigation]`
    
2. **环境版本 (Version):** `v0`
    
3. **环境类型 (Environment Type):** 这是一个并行的（Parallel）环境
        
4. **核心玩法描述 (Core Gameplay Description):**
    
        
5. **智能体 (Agents):**
    
    - **数量:** `[例如：72]` 个
        
    - **命名:** 智能体的ID是什么？ `[例如： "agent_0", "agent_1"]`
        
    - **目标:** 
        
6. **状态空间/观测空间 (State/Observation Space):**
    
    - **描述:** 每个智能体能观测到什么？是全局信息还是局部信息？
        
    - `[例如：每个智能体可以观测到整个 10x10 的网格。观测是一个 3x10x10 的NumPy数组，分别代表智能体位置层、其他智能体位置层和目标点位置层。]`
        
    - **类型和维度:** `[例如：gymnasium.spaces.Box(low=0, high=1, shape=(3, 10, 10), dtype=np.float32)]`
        
7. **动作空间 (Action Space):**
    
    - **描述:** 每个智能体可以执行哪些动作？
        
    - **类型和维度:** `[例如：gymnasium.spaces.Discrete(5)]`
        
8. **奖励机制 (Reward Mechanism):**
    
    - **描述:** 奖励是如何计算的？是团队奖励还是个体奖励？
        
    - `[例如：这是一个团队奖励。当任何一个智能体到达一个未被访问过的目标点时，所有智能体获得 +10 的奖励。为了鼓励效率，每走一步，所有智能体都会受到 -0.1 的时间惩罚。如果智能体尝试移动出边界或撞到其他智能体，会受到 -1 的惩罚。]`
        
9. **终止与截断条件 (Termination & Truncation):**
    
    - **终止 (Termination):** 什么情况下算“成功”或“失败”并结束一轮？
        
    - `[例如：当所有3个目标点都被访问过后，环境终止 (terminates)。]`
        
    - **截断 (Truncation):** 什么情况下因为超时而结束一轮？
        
    - `[例如：当总步数达到 200 步后，环境被截断 (truncates)。]`
        

**第二部分：代码结构与文件要求**

请为这个环境生成一个完整的Python项目结构。我希望代码是模块化的，易于理解和修改。

1. **项目根目录:** `[你的环境名称小写下划线]/` (例如: `cooperative_navigation/`)
    
2. **文件结构:** 请生成以下文件：
    

3. **`envs/config.py` 的要求:**
    
    - 将所有“魔法数字”（如网格大小、智能体数量、奖励值、最大步数等）都定义在这个文件中作为常量。
        
    - `cooperative_navigation_v0.py` 文件应该从这个配置文件中导入参数。这使得调整环境参数变得非常容易。
        
4. **主环境的要求:**
    
    - 创建一个继承自 `pettingzoo.AECEnv` 的主环境类。
        
    - 严格实现所有必需的方法: `__init__`, `step`, `reset`, `render`, `observe`, `close`, `action_space`, `observation_space`。
        
    - 代码需要有清晰的注释，解释关键部分的逻辑，特别是 `step` 函数中的状态转移和奖励计算。
        
5. **`__init__.py` (根目录) 的要求:**
    
    - 使用 `pettingzoo.utils.register` 函数来注册你的环境。
        
    - 这样我就可以通过 `pettingzoo.make('cooperative_navigation/CooperativeNavigation_v0')` 来方便地创建环境实例。
        

**第三部分：代码风格与最佳实践**

- **代码质量:** 生成清晰、可读性高、符合 PEP 8 规范的Python代码。
    
- **注释:** 为所有函数和复杂的逻辑块添加文档字符串（Docstrings）和注释。
    
- **依赖:** 请明确指出运行此环境需要安装哪些库（例如 `pettingzoo`, `pygame`, `numpy`, `gymnasium`）。
    
- **确定性:** 环境应当是可复现的。`reset` 方法需要接受一个可选的 `seed` 参数，并用它来重置环境的随机数生成器。
