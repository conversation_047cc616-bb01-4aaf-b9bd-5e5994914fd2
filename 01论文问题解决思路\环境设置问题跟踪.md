1，没有体现出卫星的运行状态，每个卫星都有一个Y/N的布尔值，表示当前卫星是否可用。实验会分为两部分进行，第一部分是所有卫星都是正常运行的，检查卫星的各项性能指标；第二部分是会在系统运行平稳后，一部分卫星的数据中会将Y变成N，表示该卫星不可用。所有的数据都在satellite_processed_data.csv文件夹中。

2，data文件的使用方法：
	satellite
	satellite_processed_data36_1.csv ：36个卫星正常运行的1500个时隙
	satellite_processed_data36_2.csv：36个卫星随机故障的1500个时隙
	satellite_processed_data36_3.csv：36个卫星集中故障的1500个时隙
	task
	task_ground.csv：所有地面用户正常生成任务
	task_ground1.csv：局部任务激增的任务生成
	cloud
	cloud.csv：所有云中心的地理位置
	ground
	round_station.csv：所有地面用户的地理位置
	region
	regions.json，所给策略区域的地理位置范围
	config.yaml：所有系统相关的参数配置




写一个程序，将这些数据重构，重构后的格式是
输出设置
时隙 时间 纬度 经度 光照 状态
时间只写小时分秒，时隙从1开始。
按照时隙输出，时隙1的所有卫星的状态，时隙2所有卫星的状态，最后导出成csv格式，方标其他程序的调用，状态全部是TRUE，光照在后面的光照时间范围内就是TRUE,否则就是false