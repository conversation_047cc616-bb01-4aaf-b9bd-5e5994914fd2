## SPACE2环境程序完整架构

### 目录结构总览

```
src/env/
├── Foundation_Layer/              # 基础设施层
│   ├── time_manager.py           # 时间管理器 - 统一时间上下文
│   ├── error_handling.py         # 错误处理框架 - 结构化异常管理
│   └── logging_config.py         # 日志配置 - 全局日志管理
├── physics_layer/                # 物理仿真层
│   ├── orbital.py                # 轨道动力学引擎 - ECEF坐标系
│   ├── communication_refactored.py # 通信链路管理 - 5种链路类型
│   ├── task_generator.py         # 任务生成器 - 区域任务生成模型
│   ├── task_tracking.py          # 任务跟踪系统 - 跨时隙处理
│   └── config.yaml               # 系统配置文件 - 统一参数管理
├── satellite_cloud/              # 卫星、云服务器实体
│   └── satellite.py              # DPSQ调度算法 - 动态优先级评分
├── metrics_model/                # 性能指标分析系统
│   ├── algorithm_metrics.py      # 算法指标统一接口
│   ├── latency_analyzer.py       # 延迟分析器
│   ├── energy_analyzer.py        # 能耗分析器
│   └── completion_analyzer.py    # 完成度分析器
├── task/                         # 任务管理模块
│   ├── task_tracking.py          # 任务跟踪记录系统
│   ├── task_generator.py         # 任务生成器（备用）
│   ├── config.yaml               # 任务配置文件
│   └── global_ground_stations.csv # 地面站数据（备用）
├── env_data/                     # 环境数据存储
│   ├── satellite_data72_1.csv    # 72颗LEO卫星×1441时隙 = 103,752条记录
│   ├── global_ground_stations.csv # 420个全球地面用户终端
│   └── cloud_station.csv         # 5个云计算中心坐标
└── space2_env.py                 # PettingZoo环境接口（规划中）
```

### 核心模块功能映射

#### 1. 基础设施层 (Foundation_Layer/)
- **time_manager.py**: 提供 `TimeContext` 和 `TimeManager` 类，管理仿真时间上下文
- **error_handling.py**: 统一异常处理机制，结构化错误管理
- **logging_config.py**: 全局日志配置，支持多级别输出和默认配置加载

#### 2. 物理仿真层 (physics_layer/)
- **orbital.py**: 
  - 核心类：`OrbitalUpdater`, `Satellite`, `GroundStation`
  - 功能：ECEF坐标系三维距离计算，三种可见性矩阵生成
  - 数据源：`satellite_data72_1.csv` (103,752条记录)
- **communication_refactored.py**:
  - 核心类：`CommunicationManager`
  - 功能：5种链路类型通信性能计算，真实物理参数建模
- **task_generator.py**:
  - 核心类：`TaskGenerator`, `Location`
  - 功能：基于区域任务生成模型，为420个地理坐标点生成任务
- **task_tracking.py**:
  - 功能：任务生命周期管理，支持跨时隙处理和部分处理
- **config.yaml**: 系统核心配置文件，包含所有模块参数

#### 3. 应用层模块
- **satellite_cloud/satellite.py**:
  - 核心类：`Satellite`, `DPSQScheduler`, `SatelliteTask`
  - 功能：DPSQ动态优先级评分调度算法实现
- **metrics_model/**:
  - 统一的算法性能评估接口
  - 多维度指标分析（延迟、能耗、完成率）

#### 4. 任务管理模块 (task/)
- **task_tracking.py**: 使用 `TaskGenerator` 实时生成任务，支持真实轨道可见性数据
- **备用配置和数据文件**: 独立的配置管理

#### 5. 数据存储 (env_data/)
- **satellite_data72_1.csv**: 格式 `satellite_ID,time_slot,time,lat,lon,light,state`
- **global_ground_stations.csv**: 格式 `ID,Latitude,Longitude,RegionType,Size,PurposeType`
- **cloud_station.csv**: 格式 `ID,Latitude,Longitude`

### 模块间依赖关系

```
env_data/ → Foundation_Layer/ → physics_layer/ → satellite_cloud/ & metrics_model/
    ↓              ↓                    ↓                    ↓
基础数据    →    时间/日志管理    →    物理仿真引擎    →    调度算法/性能分析
```

### 关键技术特征
- **数据规模**: 103,752条卫星记录，420个地面站，5个云中心
- **仿真参数**: 1441时隙，5秒/时隙，总计7205秒仿真时间
- **可见性阈值**: 卫星间5500km，卫星-地面2500km，卫星-云3300km
- **架构模式**: 三层解耦，模块化设计，数据驱动
````


