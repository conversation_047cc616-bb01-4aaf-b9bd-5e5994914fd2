# Dynamic Action Space 理论分析与设计

  

## 1. 核心概念与数学模型

  

### 1.1 动态动作空间定义

  

在时刻 $t$，卫星 $i$ 的动作空间 $\mathcal{A}_i(t)$ 是一个动态变化的集合，定义为：

  

$$\mathcal{A}_i(t) = \{a_0, a_1, ..., a_7\}$$

  

其中每个动作 $a_k$ 映射到一个目标 $(type_k, index_k)$：

  

- $a_0$: 本地处理 $(local, i)$ - **始终可用**

- $a_1$ ~ $a_5$: 邻居卫星 $(satellite, j)$ 其中 $j \in \mathcal{N}_i(t)$

- $a_6$: 云中心 $(cloud, c)$ 其中 $c \in \mathcal{C}_i(t)$

- $a_7$: 丢弃任务 $(drop, \varnothing)$ - **始终可用**

  

### 1.2 可见性约束

  

动作空间受可见性矩阵约束：

  

**卫星间可见性矩阵**：

$$V^{sat}(t) \in \{0,1\}^{72 \times 72}$$

  

其中 $V^{sat}_{ij}(t) = 1$ 表示卫星 $i$ 和 $j$ 在时刻 $t$ 可见。

  

**卫星-云可见性矩阵**：

$$V^{cloud}(t) \in \{0,1\}^{72 \times 5}$$

  

其中 $V^{cloud}_{ic}(t) = 1$ 表示卫星 $i$ 和云中心 $c$ 在时刻 $t$ 可见。

  

### 1.3 卫星状态约束

  

引入卫星状态向量：

$$S(t) \in \{0,1\}^{72}$$

  

其中 $S_i(t) = 1$ 表示卫星 $i$ 正常运行，$S_i(t) = 0$ 表示故障。

  

**有效可见性**（考虑故障状态）：

$$\tilde{V}^{sat}_{ij}(t) = V^{sat}_{ij}(t) \cdot S_j(t)$$

  

即只有正常运行的卫星才能作为有效的卸载目标。

  

## 2. 邻居选择策略分析

  

### 2.1 策略空间定义

  

给定可见卫星集合 $\mathcal{V}_i(t) = \{j | \tilde{V}^{sat}_{ij}(t) = 1, j \neq i\}$，需要选择最多5个邻居。

  

当 $|\mathcal{V}_i(t)| > 5$ 时，需要选择策略 $\pi: \mathcal{V}_i(t) \rightarrow \mathcal{N}_i(t)$，其中 $|\mathcal{N}_i(t)| \leq 5$。

  

### 2.2 基于距离的选择策略

  

**距离矩阵**：

$$D^{sat}(t) \in \mathbb{R}^{72 \times 72}_+$$

  

**选择算法**：

```

输入: 可见卫星集合 V_i(t), 距离矩阵 D(t)

输出: 邻居集合 N_i(t)

  

1. 计算有效距离: d_ij = D_ij if j ∈ V_i(t) else ∞

2. 排序: sorted_neighbors = argsort(d_ij)

3. 选择: N_i(t) = sorted_neighbors[:5]

```

  

**优势**：

- 最小化通信延迟（距离越近，传输时间越短）

- 链路质量通常更好

- 物理直觉明确

  

**劣势**：

- 可能导致局部拥塞

- 不考虑目标卫星的负载

  

### 2.3 基于负载的选择策略

  

**负载向量**：

$$L(t) = [l_1(t), l_2(t), ..., l_{72}(t)]$$

  

其中 $l_i(t) = \frac{queue\_length_i(t)}{max\_queue\_length} \cdot w_1 + cpu\_usage_i(t) \cdot w_2$

  

**选择算法**：

```

输入: 可见卫星集合 V_i(t), 负载向量 L(t)

输出: 邻居集合 N_i(t)

  

1. 计算有效负载: l_j = L_j if j ∈ V_i(t) else ∞

2. 排序: sorted_neighbors = argsort(l_j)  # 升序，选择负载最低的

3. 选择: N_i(t) = sorted_neighbors[:5]

```

  

**优势**：

- 自然的负载均衡

- 减少任务等待时间

- 提高系统整体吞吐量

  

**劣势**：

- 可能选择距离较远的卫星

- 需要实时负载信息

  

### 2.4 混合选择策略

  

**综合评分函数**：

$$score_{ij}(t) = \alpha \cdot \frac{1}{D_{ij}(t)} + \beta \cdot (1 - L_j(t)) + \gamma \cdot Q_{ij}(t)$$

  

其中：

- $D_{ij}(t)$: 归一化距离

- $L_j(t)$: 归一化负载

- $Q_{ij}(t)$: 链路质量（可选）

- $\alpha + \beta + \gamma = 1$: 权重参数

  

**选择算法**：

```

输入: V_i(t), D(t), L(t), Q(t)

输出: N_i(t)

  

1. 对每个 j ∈ V_i(t):

   计算 score_ij = α/D_ij + β(1-L_j) + γQ_ij

2. 排序: sorted_neighbors = argsort(score_ij, descending=True)

3. 选择: N_i(t) = sorted_neighbors[:5]

```

  

### 2.5 策略选择的理论依据

  

**定理1（负载均衡）**：

在稳态条件下，基于负载的选择策略可以证明收敛到Nash均衡，其中所有活跃卫星的负载趋于相等。

  

**定理2（延迟最优）**：

在轻负载条件下（$\rho < 0.5$），基于距离的选择策略可以最小化平均任务完成时间。

  

**定理3（混合最优）**：

存在最优权重 $(\alpha^*, \beta^*, \gamma^*)$ 使得混合策略在多目标优化意义下Pareto最优。

  

## 3. 云中心选择机制

  

### 3.1 云中心选择问题

  

给定可见云中心集合 $\mathcal{C}_i(t) = \{c | V^{cloud}_{ic}(t) = 1\}$，选择一个最优云中心。

  

### 3.2 选择准则

  

**准则1：最近距离**

$$c^* = \arg\min_{c \in \mathcal{C}_i(t)} D^{cloud}_{ic}(t)$$

  

**准则2：最低负载**

$$c^* = \arg\min_{c \in \mathcal{C}_i(t)} L^{cloud}_c(t)$$

  

**准则3：综合最优**

$$c^* = \arg\max_{c \in \mathcal{C}_i(t)} \left( \frac{\lambda_1}{D^{cloud}_{ic}(t)} + \lambda_2(1 - L^{cloud}_c(t)) \right)$$

  

### 3.3 理论分析

  

云中心具有以下特性：

- **高计算能力**：$f_{cloud} = 100$ TOPS vs $f_{sat} = 10$ TOPS

- **高能耗成本**：传输到云的能耗通常更高

- **高延迟**：包括传输延迟和排队延迟

  

**权衡分析**：

- 适合云处理的任务：计算密集型、非实时性

- 不适合云处理的任务：数据密集型、实时性要求高

  

## 4. 动作掩码机制

  

### 4.1 动作掩码定义

  

动作掩码 $M_i(t) \in \{0,1\}^8$ 表示每个动作的有效性：

  

$$M_i(t)[k] = \begin{cases}

1, & \text{if action } a_k \text{ is valid} \\

0, & \text{otherwise}

\end{cases}$$

  

### 4.2 掩码计算规则

  

```python

def compute_action_mask(i, t):

    M = [0] * 8

    # 1. 本地处理始终可用（除非卫星故障）

    M[0] = S_i(t)

    # 2. 邻居卫星（1-5）

    neighbors = select_neighbors(i, t)

    for k in range(1, 6):

        if k-1 < len(neighbors):

            M[k] = 1

        else:

            M[k] = 0

    # 3. 云中心（6）

    if exists_visible_cloud(i, t):

        M[6] = 1

    else:

        M[6] = 0

    # 4. 丢弃始终可用

    M[7] = 1

    return M

```

  

### 4.3 故障卫星的特殊处理

  

当卫星 $i$ 故障时（$S_i(t) = 0$）：

- 不生成动作（不作为智能体）

- 不出现在其他卫星的邻居列表中

- 本地任务累积或自动丢弃

  

## 5. 动作映射表

  

### 5.1 映射表结构

  

动作映射表 $\Phi_i(t): \{0,1,...,7\} \rightarrow \{(type, index)\}$

  

示例：

```

Φ_i(t) = {

    0: ('local', i),

    1: ('satellite', 23),

    2: ('satellite', 45),

    3: ('satellite', 12),

    4: ('satellite', None),  # 无效（邻居不足5个）

    5: ('satellite', None),  # 无效

    6: ('cloud', 2),

    7: ('drop', None)

}

```

  

### 5.2 一致性保证

  

**关键原则**：动作映射必须与观测中的邻居顺序严格一致。

  

设观测中的邻居状态矩阵为 $O_{neighbor} \in \mathbb{R}^{5 \times 7}$，则：

- $O_{neighbor}[0]$ 对应 $\Phi_i(t)[1]$ 的目标卫星状态

- $O_{neighbor}[1]$ 对应 $\Phi_i(t)[2]$ 的目标卫星状态

- ...

- $O_{neighbor}[4]$ 对应 $\Phi_i(t)[5]$ 的目标卫星状态

  

## 6. 性能优化考虑

  

### 6.1 计算复杂度分析

  

**邻居选择**：

- 基于距离：$O(n \log n)$，其中 $n = |\mathcal{V}_i(t)| \leq 71$

- 基于负载：$O(n \log n)$

- 混合策略：$O(n \log n)$

  

**总复杂度**：

对所有72个卫星：$O(72 \times n \log n) = O(n^2 \log n)$

  

### 6.2 缓存策略

  

可以缓存的内容：

1. 距离矩阵（每个时隙计算一次）

2. 可见性矩阵（由orbital模块提供）

3. 邻居选择结果（在同一时隙内不变）

  

### 6.3 向量化实现

  

利用NumPy的向量化操作：

```python

# 批量计算所有卫星的邻居

def batch_select_neighbors(V_sat, D_sat, S):

    # 应用故障掩码

    V_effective = V_sat * S.reshape(1, -1)

    # 批量排序

    sorted_indices = np.argsort(D_sat, axis=1)

    # 批量选择前5个

    neighbors = sorted_indices[:, 1:6]  # 排除自己

    return neighbors

```

  

## 7. 扩展性设计

  

### 7.1 支持不同卫星数量

  

设计应支持可变卫星数量 $N_{sat} \in \{18, 36, 72, 144, ...\}$：

  

```python

class DynamicActionSpace:

    def __init__(self, config):

        self.num_satellites = config['system']['num_leo_satellites']

        self.num_neighbors = min(5, self.num_satellites - 1)

```

  

### 7.2 支持不同选择策略

  

策略模式设计：

```python

class NeighborSelector(ABC):

    @abstractmethod

    def select(self, satellite_idx, visibility, **kwargs):

        pass

  

class DistanceBasedSelector(NeighborSelector):

    def select(self, satellite_idx, visibility, distances):

        # 实现基于距离的选择

class LoadBasedSelector(NeighborSelector):

    def select(self, satellite_idx, visibility, loads):

        # 实现基于负载的选择

class HybridSelector(NeighborSelector):

    def select(self, satellite_idx, visibility, distances, loads):

        # 实现混合选择

```

  

## 8. 验证与测试策略

  

### 8.1 正确性验证

  

需要验证的属性：

1. **动作空间大小恒定**：始终为8个动作

2. **掩码一致性**：$M[k] = 0 \Rightarrow \Phi[k] = invalid$

3. **邻居数量约束**：$|\mathcal{N}_i(t)| \leq 5$

4. **故障隔离**：故障卫星不出现在邻居列表中

  

### 8.2 性能基准

  

测试场景：

1. **完全可见**：所有卫星互相可见

2. **稀疏可见**：平均每个卫星可见5-10个邻居

3. **极端稀疏**：部分卫星孤立

4. **大规模故障**：50%卫星故障

  

### 8.3 边界条件

  

需要处理的边界情况：

1. 卫星完全孤立（无可见邻居和云）

2. 所有邻居都故障

3. 可见邻居少于5个

4. 多个云中心可见

5. 卫星自身故障

  

## 9. 与其他组件的接口

  

### 9.1 输入接口

  

```python

def compute_action_space(

    satellite_idx: int,

    visibility_matrices: Dict[str, np.ndarray],

    satellite_status: np.ndarray,

    distances: Optional[Dict[str, np.ndarray]] = None,

    loads: Optional[np.ndarray] = None

) -> Tuple[Dict[int, Tuple[str, Optional[int]]], np.ndarray]:

    """

    输入:

        satellite_idx: 卫星索引

        visibility_matrices: {'sat_to_sat': (72,72), 'sat_to_cloud': (72,5)}

        satellite_status: (72,) 卫星状态向量

        distances: 可选，用于邻居选择

        loads: 可选，用于负载均衡

    输出:

        action_mapping: 动作索引到目标的映射

        action_mask: 动作有效性掩码

    """

```

  

### 9.2 输出接口

  

输出必须满足：

1. action_mapping 包含8个条目

2. action_mask 是长度为8的布尔数组

3. 映射顺序与观测构建器约定一致

  

## 10. 理论保证与性质

  

### 性质1：动作空间的有限性

$$|\mathcal{A}_i(t)| = 8, \forall i, t$$

  

### 性质2：最小可用动作

即使在最坏情况下（完全孤立），至少有2个动作可用：本地处理和丢弃。

  

### 性质3：负载均衡收敛性

在使用基于负载的邻居选择策略时，系统负载分布的方差单调递减：

$$Var(L(t+1)) \leq Var(L(t))$$

  

### 性质4：故障隔离性

故障卫星的存在不影响正常卫星的动作空间计算复杂度。

  

## 总结

  

DynamicActionSpace是整个PettingZoo环境的基础组件，它：

1. 定义了智能体的行动边界

2. 实现了动态适应网络拓扑变化

3. 支持多种邻居选择策略

4. 处理卫星故障情况

5. 保证与观测的一致性

  

其设计原则是：

- **简单性**：固定8个动作位置

- **灵活性**：支持多种选择策略

- **鲁棒性**：处理各种边界情况

- **高效性**：支持向量化和缓存

  

这个组件为后续的观测构建、动作处理和奖励计算奠定了坚实的基础。