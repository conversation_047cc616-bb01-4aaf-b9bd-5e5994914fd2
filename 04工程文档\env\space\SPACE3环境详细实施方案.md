  SPACE3环境详细实施方案

  一、核心架构澄清

  1.1 任务分配逻辑的替换关系

  # 传统模式（现有系统）
  TaskGenerator → TaskDistributor → SatelliteCompute

  # RL模式（新系统）
  TaskGenerator → RL Agent决策 → SequenceActionAdapter →
  SatelliteCompute
                       ↑                      ↓
                (观测构建与Padding)    (动作解析与验证)

  关键点：TaskDistributor在RL模式下被完全绕过，只保留其部分辅     
  助函数（如验证可见性）。

  二、详细实施计划

  2.1 Phase 1: space3_env.py 主环境实现

  # src/env/space3_env.py

  import numpy as np
  from pettingzoo import ParallelEnv
  from gymnasium import spaces
  from typing import Dict, List, Optional, Tuple, Any
  import yaml

  from src.env.Foundation_Layer.time_manager import
  create_time_manager_from_config
  from src.env.physics_layer.orbital import OrbitalUpdater        
  from src.env.physics_layer.communication_refactored import      
  CommunicationManager
  from src.env.physics_layer.task_generator import
  TaskGenerator
  from src.env.physics_layer.task_models import Task,
  TaskStatus
  from src.env.satellite_cloud.satellite_compute import
  SatelliteCompute
  from src.env.satellite_cloud.cloud_compute import
  CloudCompute
  from src.env.metrics_model.algorithm_metrics import
  AlgorithmMetrics

  class Space3Env(ParallelEnv):
      """
      SPACE3 PettingZoo环境
      支持序列动作的混合模式环境
      """

      metadata = {
          "render_modes": ["human", "rgb_array"],
          "name": "space3_v0",
      }

      def __init__(self, config_path: str =
  "src/env/physics_layer/config.yaml"):
          super().__init__()

          # 1. 加载配置
          with open(config_path, 'r') as f:
              self.config = yaml.safe_load(f)

          # 2. 初始化现有模块
          self.time_manager =
  create_time_manager_from_config(self.config)
          self.orbital_updater = OrbitalUpdater()
          self.communication_manager =
  CommunicationManager(self.config)
          self.task_generator = TaskGenerator(self.config)        

          # 3. 初始化卫星计算节点
          self.num_satellites =
  self.config['system']['num_leo_satellites']
          self.satellites = {}
          for i in range(self.num_satellites):
              sat_id = f"sat_{i:03d}"
              self.satellites[sat_id] = SatelliteCompute(
                  satellite_id=sat_id,
                  config=self.config
              )

          # 4. 初始化云计算节点
          self.num_clouds =
  self.config['system']['num_cloud_centers']
          self.cloud_nodes = {}
          for i in range(self.num_clouds):
              cloud_id = f"cloud_{i}"
              self.cloud_nodes[cloud_id] = CloudCompute(
                  cloud_id=cloud_id,
                  config=self.config
              )

          # 5. RL环境参数
          self.max_queue_size = self.config.get('rl_env',
  {}).get('max_queue_size', 20)
          self.task_feature_dim = 8  # 任务特征维度
          self.state_feature_dim = 10  # 卫星状态特征维度

          # 6. 动作空间目标映射
          self.num_neighbors =
  self.config.get('communication',
  {}).get('max_visible_neighbors', 10)
          self.num_ground_stations =
  self.config['system']['num_ground_users']
          self.action_targets = self._build_action_targets()      
          self.num_action_targets = len(self.action_targets)      

          # 7. PettingZoo必需属性
          self.possible_agents = list(self.satellites.keys())     
          self.agents = self.possible_agents[:]

          # 8. 定义空间
          self._setup_spaces()

          # 9. 任务缓存
          self.task_queues = {agent: [] for agent in
  self.agents}
          self.current_timeslot = 0

          # 10. 性能追踪
          self.metrics_analyzer = AlgorithmMetrics()
          self.episode_data = []

      def _build_action_targets(self) -> Dict[int, str]:
          """构建动作目标映射"""
          targets = {}
          idx = 0

          # 0: 本地处理
          targets[idx] = "local"
          idx += 1

          # 1-N: 邻居卫星
          for i in range(self.num_neighbors):
              targets[idx] = f"neighbor_{i}"
              idx += 1

          # N+1-M: 地面站转发到云
          for i in range(min(5, self.num_clouds)):  #
  最多5个云选项
              targets[idx] = f"cloud_{i}"
              idx += 1

          return targets

      def _setup_spaces(self):
          """设置观测和动作空间"""
          # 观测空间：字典空间
          self.observation_spaces = {}
          for agent in self.possible_agents:
              self.observation_spaces[agent] = spaces.Dict({      
                  # 卫星自身状态
                  'own_state': spaces.Box(
                      low=-np.inf, high=np.inf,
                      shape=(self.state_feature_dim,),
                      dtype=np.float32
                  ),

                  # 任务队列（固定最大长度，带padding）
                  'task_queue': spaces.Box(
                      low=-np.inf, high=np.inf,
                      shape=(self.max_queue_size,
  self.task_feature_dim),
                      dtype=np.float32
                  ),

                  # 任务掩码（标识哪些是真实任务）
                  'task_mask': spaces.Box(
                      low=0, high=1,
                      shape=(self.max_queue_size,),
                      dtype=np.int8
                  ),

                  # 动作掩码（每个任务的可行动作）
                  'action_mask': spaces.Box(
                      low=0, high=1,
                      shape=(self.max_queue_size,
  self.num_action_targets),
                      dtype=np.int8
                  ),

                  # 邻居信息
                  'neighbor_states': spaces.Box(
                      low=-np.inf, high=np.inf,
                      shape=(self.num_neighbors, 5),  #
  简化的邻居状态
                      dtype=np.float32
                  ),

                  # 时间信息
                  'time_info': spaces.Box(
                      low=0, high=np.inf,
                      shape=(2,),  # [当前时隙, 剩余时隙]
                      dtype=np.float32
                  )
              })

          # 动作空间：序列动作（MultiDiscrete）
          self.action_spaces = {}
          for agent in self.possible_agents:
              self.action_spaces[agent] =
  spaces.MultiDiscrete(
                  [self.num_action_targets] *
  self.max_queue_size
              )

      def reset(self, seed=None, options=None):
          """重置环境"""
          # 1. 重置时间
          self.current_timeslot = 0

          # 2. 重置所有卫星
          for sat_id, satellite in self.satellites.items():       
              satellite.reset()

          # 3. 重置云节点
          for cloud_id, cloud in self.cloud_nodes.items():        
              cloud.reset()

          # 4. 清空任务队列
          self.task_queues = {agent: [] for agent in
  self.agents}

          # 5. 生成初始任务
          self._generate_and_assign_initial_tasks()

          # 6. 构建初始观测
          observations = self._build_observations()

          # 7. 初始化info
          infos = {agent: {} for agent in self.agents}

          return observations, infos

      def step(self, actions: Dict[str, np.ndarray]):
          """
          执行一步仿真
          actions: {agent_id: action_sequence}
          """
          # 1. 获取当前时间上下文
          time_context =
  self.time_manager.get_context(self.current_timeslot)

          # 2. 更新物理层（轨道、通信）
          self.orbital_updater.update_positions(time_context)     
          visibility_matrices = self.orbital_updater.get_visi     
  bility_matrices(self.current_timeslot)
          comm_matrix =
  self.communication_manager.calculate_communication_matrix(      
              visibility_matrices, time_context
          )

          # 3. 处理动作序列（关键：替代TaskDistributor）
          rewards = {}
          for agent_id, action_seq in actions.items():
              reward = self._process_agent_actions(
                  agent_id, action_seq, visibility_matrices,      
  comm_matrix
              )
              rewards[agent_id] = reward

          # 4. 执行卫星计算（DPSQ调度）
          for sat_id, satellite in self.satellites.items():       
              satellite.process_timeslot(time_context)

          # 5. 执行云计算
          for cloud_id, cloud in self.cloud_nodes.items():        
              cloud.process_tasks(time_context)

          # 6. 生成新任务（下一时隙）
          if self.current_timeslot <
  self.config['system']['num_timeslots'] - 1:
              new_tasks =
  self.task_generator.generate_tasks_for_timeslot(
                  self.current_timeslot + 1
              )
              self._assign_tasks_to_satellites(new_tasks,
  visibility_matrices)

          # 7. 收集性能数据
          step_metrics = self._collect_step_metrics()
          self.episode_data.append(step_metrics)

          # 8. 计算奖励（基于AlgorithmMetrics）
          rewards = self._calculate_rewards(step_metrics,
  rewards)

          # 9. 更新时间
          self.current_timeslot += 1

          # 10. 检查终止条件
          terminations = {
              agent: self.current_timeslot >=
  self.config['system']['num_timeslots']
              for agent in self.agents
          }
          truncations = {agent: False for agent in
  self.agents}

          # 11. 构建新观测
          observations = self._build_observations()

          # 12. 构建info
          infos = {
              agent: {
                  'tasks_completed':
  step_metrics.get(f'{agent}_completed', 0),
                  'tasks_dropped':
  step_metrics.get(f'{agent}_dropped', 0),
                  'energy_consumed':
  step_metrics.get(f'{agent}_energy', 0),
              }
              for agent in self.agents
          }

          return observations, rewards, terminations,
  truncations, infos

      def _build_observations(self) -> Dict[str, Dict]:
          """构建所有智能体的观测（带Padding和Masking）"""        
          observations = {}

          for agent_id in self.agents:
              satellite = self.satellites[agent_id]

              # 1. 卫星自身状态
              sat_status = satellite.get_status()
              own_state = np.array([
                  sat_status.get('cpu_usage', 0) / 100.0,
                  sat_status.get('memory_usage', 0) / 100.0,      
                  sat_status.get('battery_level', 0) / 100.0,     
                  sat_status.get('queue_length', 0) /
  self.max_queue_size,
                  sat_status.get('processing_tasks', 0) /
  10.0,
                  sat_status.get('dropped_tasks', 0) / 100.0,     
                  sat_status.get('completed_tasks', 0) /
  100.0,
                  sat_status.get('total_energy_consumed', 0)      
  / 1000.0,
                  self.current_timeslot /
  self.config['system']['num_timeslots'],
                  1.0 if sat_status.get('in_sunlight', False)     
   else 0.0
              ], dtype=np.float32)

              # 2. 任务队列编码（关键：Padding到固定长度）        
              task_queue = self.task_queues[agent_id]
              task_features = np.zeros((self.max_queue_size,      
  self.task_feature_dim), dtype=np.float32)
              task_mask = np.zeros(self.max_queue_size,
  dtype=np.int8)

              for i, task in
  enumerate(task_queue[:self.max_queue_size]):
                  task_features[i] = self._encode_task(task)      
                  task_mask[i] = 1  # 标记为真实任务

              # 3. 动作掩码（基于可见性）
              action_mask =
  self._compute_action_mask(agent_id, task_queue)

              # 4. 邻居状态
              neighbor_states =
  self._get_neighbor_states(agent_id)

              # 5. 时间信息
              time_info = np.array([
                  self.current_timeslot,
                  self.config['system']['num_timeslots'] -        
  self.current_timeslot
              ], dtype=np.float32)

              observations[agent_id] = {
                  'own_state': own_state,
                  'task_queue': task_features,
                  'task_mask': task_mask,
                  'action_mask': action_mask,
                  'neighbor_states': neighbor_states,
                  'time_info': time_info
              }

          return observations

      def _process_agent_actions(self, agent_id: str,
  action_seq: np.ndarray,
                                visibility_matrices: Dict,        
  comm_matrix: np.ndarray) -> float:
          """
          处理智能体的动作序列（替代TaskDistributor）
          这是RL模式的核心：智能体决策直接转换为任务分配
          """
          satellite = self.satellites[agent_id]
          task_queue = self.task_queues[agent_id]

          if len(task_queue) == 0:
              return 0.0

          total_reward = 0.0
          sat_idx = int(agent_id.split('_')[1])

          # 获取实际任务数（去除padding）
          actual_task_count = min(len(task_queue),
  self.max_queue_size)

          for task_idx in range(actual_task_count):
              task = task_queue[task_idx]
              target_id = action_seq[task_idx]
              target_type = self.action_targets[target_id]        

              # 根据目标类型处理任务
              if target_type == "local":
                  # 本地处理
                  success =
  satellite.add_task(self._task_to_compute_task(task))
                  total_reward += 1.0 if success else -0.5        

              elif target_type.startswith("neighbor"):
                  # 转发到邻居卫星
                  neighbor_idx =
  int(target_type.split('_')[1])
                  visible_neighbors =
  self._get_visible_neighbors(sat_idx, visibility_matrices)       

                  if neighbor_idx < len(visible_neighbors):       
                      neighbor_sat_id =
  f"sat_{visible_neighbors[neighbor_idx]:03d}"
                      neighbor_sat =
  self.satellites[neighbor_sat_id]
                      success =
  neighbor_sat.add_task(self._task_to_compute_task(task))
                      total_reward += 0.8 if success else
  -0.5
                  else:
                      total_reward -= 1.0  # 无效动作惩罚

              elif target_type.startswith("cloud"):
                  # 转发到云（通过地面站）
                  cloud_idx = int(target_type.split('_')[1])      
                  if self._check_ground_visibility(sat_idx,       
  visibility_matrices):
                      cloud_id = f"cloud_{cloud_idx}"
                      if cloud_id in self.cloud_nodes:
                          self.cloud_nodes[cloud_id].add_task     
  (self._task_to_compute_task(task))
                          total_reward += 0.6
                  else:
                      total_reward -= 1.0  # 不可见惩罚

          # 清空已处理的任务队列
          self.task_queues[agent_id] = []

          return total_reward

      def _calculate_rewards(self, step_metrics: Dict,
  initial_rewards: Dict) -> Dict:
          """
          基于AlgorithmMetrics计算综合奖励
          """
          rewards = {}

          for agent_id in self.agents:
              # 基础奖励（动作执行）
              base_reward = initial_rewards.get(agent_id,
  0.0)

              # 性能奖励（从metrics获取）
              completion_bonus =
  step_metrics.get(f'{agent_id}_completion_rate', 0) * 10
              delay_penalty =
  -step_metrics.get(f'{agent_id}_avg_delay', 0) * 0.01
              energy_penalty =
  -step_metrics.get(f'{agent_id}_energy_per_task', 0) * 0.001     

              # 综合奖励
              rewards[agent_id] = (
                  base_reward +
                  self.config.get('rl_env',
  {}).get('completion_weight', 1.0) * completion_bonus +
                  self.config.get('rl_env',
  {}).get('delay_weight', 0.5) * delay_penalty +
                  self.config.get('rl_env',
  {}).get('energy_weight', 0.3) * energy_penalty
              )

          return rewards

      # ========== 辅助方法 ==========

      def _encode_task(self, task: Task) -> np.ndarray:
          """将任务编码为特征向量"""
          return np.array([
              task.type_id.value / 3.0,  # 归一化任务类型
              task.priority / 5.0,
              task.data_size_mb / 100.0,
              task.complexity_cycles_per_bit / 1000.0,
              (task.deadline_timestamp -
  self.current_timeslot * 3) / 3600.0,
              task.coordinates[0] / 90.0,  # 纬度
              task.coordinates[1] / 180.0,  # 经度
              1.0 if hasattr(task, 'is_urgent') else 0.0
          ], dtype=np.float32)

      def _compute_action_mask(self, agent_id: str,
  task_queue: List) -> np.ndarray:
          """计算动作掩码"""
          mask = np.zeros((self.max_queue_size,
  self.num_action_targets), dtype=np.int8)

          # 对每个真实任务设置可行动作
          for i in range(min(len(task_queue),
  self.max_queue_size)):
              # 本地处理总是可行
              mask[i, 0] = 1

              # 根据可见性设置其他选项
              # TODO: 基于实际可见性矩阵细化
              mask[i, 1:self.num_neighbors+1] = 1  #
  简化：假设所有邻居可见
              mask[i, self.num_neighbors+1:] = 1  #
  简化：假设云可达

          return mask

      def _get_visible_neighbors(self, sat_idx: int,
  visibility_matrices: Dict) -> List[int]:
          """获取可见邻居卫星索引"""
          sat_to_sat =
  visibility_matrices['satellite_to_satellite']
          visible = np.where(sat_to_sat[sat_idx] > 0)[0]
          return visible.tolist()

      def _check_ground_visibility(self, sat_idx: int,
  visibility_matrices: Dict) -> bool:
          """检查是否有地面站可见"""
          sat_to_ground =
  visibility_matrices['satellite_to_ground']
          return np.any(sat_to_ground[sat_idx] > 0)

      def _get_neighbor_states(self, agent_id: str) ->
  np.ndarray:
          """获取邻居卫星简化状态"""
          # 简化实现：返回固定大小的邻居状态矩阵
          neighbor_states = np.zeros((self.num_neighbors, 5),     
   dtype=np.float32)
          # TODO: 从实际邻居卫星获取状态
          return neighbor_states

      def _task_to_compute_task(self, task: Task):
          """将TaskGenerator的Task转换为ComputeTask"""
          # TODO: 实现转换逻辑
          from src.env.satellite_cloud.compute_models import      
  ComputeTask
          return ComputeTask(
              task_id=task.task_id,
              priority=task.priority,
              deadline=task.deadline_timestamp,
              data_size_mb=task.data_size_mb,
              complexity=task.complexity_cycles_per_bit *
  task.data_size_mb * 8 * 1e6,
              drop_penalty=50.0,
              arrival_time=task.generation_time
          )

      def _generate_and_assign_initial_tasks(self):
          """生成并分配初始任务"""
          initial_tasks =
  self.task_generator.generate_tasks_for_timeslot(0)
          # 简单分配：轮询分配到卫星
          for i, task in enumerate(initial_tasks):
              sat_idx = i % self.num_satellites
              sat_id = f"sat_{sat_idx:03d}"
              if sat_id not in self.task_queues:
                  self.task_queues[sat_id] = []
              self.task_queues[sat_id].append(task)

      def _assign_tasks_to_satellites(self, tasks:
  List[Task], visibility_matrices: Dict):
          """将新生成的任务分配到可见卫星的队列"""
          # 这里使用简单的分配策略
          #
  在RL模式下，这些任务会在下一步由智能体决定如何处理
          for task in tasks:
              # 找到最近的可见卫星
              # 简化：轮询分配
              sat_idx = task.location_id %
  self.num_satellites
              sat_id = f"sat_{sat_idx:03d}"
              if len(self.task_queues[sat_id]) <
  self.max_queue_size:
                  self.task_queues[sat_id].append(task)

      def _collect_step_metrics(self) -> Dict:
          """收集当前步的性能指标"""
          metrics = {}
          for sat_id, satellite in self.satellites.items():       
              status = satellite.get_status()
              metrics[f'{sat_id}_completed'] =
  status.get('completed_tasks', 0)
              metrics[f'{sat_id}_dropped'] =
  status.get('dropped_tasks', 0)
              metrics[f'{sat_id}_energy'] =
  status.get('energy_consumed', 0)
              metrics[f'{sat_id}_queue_length'] =
  len(self.task_queues[sat_id])
          return metrics

      def render(self):
          """可选的渲染方法"""
          if self.render_mode == "human":
              print(f"Timeslot: {self.current_timeslot}")
              for sat_id in self.agents[:3]:  #
  只显示前3个卫星
                  print(f"  {sat_id}:
  Queue={len(self.task_queues[sat_id])}")

  2.2 Phase 2: sequence_action_adapter.py 实现

  # src/env/sequence_action_adapter.py

  import numpy as np
  from typing import List, Dict, Tuple, Optional
  from dataclasses import dataclass

  @dataclass
  class TaskAssignment:
      """任务分配决策"""
      task_id: int
      source_satellite: str
      target_type: str  # 'local', 'neighbor', 'cloud'
      target_id: Optional[str]
      feasible: bool
      estimated_cost: float

  class SequenceActionAdapter:
      """
      序列动作适配器
      将RL智能体的序列动作转换为系统可执行的分配指令
      """

      def __init__(self, config: Dict):
          self.config = config
          self.num_satellites =
  config['system']['num_leo_satellites']
          self.max_neighbors = config.get('communication',        
  {}).get('max_visible_neighbors', 10)

      def validate_and_convert(self,
                              agent_id: str,
                              action_sequence: np.ndarray,        
                              task_queue: List,
                              visibility_matrix: np.ndarray,      
                              comm_matrix: np.ndarray) ->
  List[TaskAssignment]:
          """
          验证并转换动作序列

          Args:
              agent_id: 卫星ID
              action_sequence: 动作序列 [task1_target,
  task2_target, ...]
              task_queue: 任务队列
              visibility_matrix: 可见性矩阵
              comm_matrix: 通信质量矩阵

          Returns:
              验证后的任务分配列表
          """
          assignments = []
          sat_idx = int(agent_id.split('_')[1])

          # 获取实际任务数（去除padding）
          actual_tasks = min(len(task_queue),
  len(action_sequence))

          for task_idx in range(actual_tasks):
              task = task_queue[task_idx]
              target_action = action_sequence[task_idx]

              # 解析目标类型
              assignment = self._parse_action(
                  task, agent_id, target_action,
                  sat_idx, visibility_matrix, comm_matrix
              )

              assignments.append(assignment)

          return assignments

      def _parse_action(self, task, agent_id: str, action:        
  int,
                       sat_idx: int, visibility_matrix:
  np.ndarray,
                       comm_matrix: np.ndarray) ->
  TaskAssignment:
          """解析单个动作"""

          # 动作0：本地处理
          if action == 0:
              return TaskAssignment(
                  task_id=task.task_id,
                  source_satellite=agent_id,
                  target_type='local',
                  target_id=agent_id,
                  feasible=True,

  estimated_cost=self._estimate_local_cost(task)
              )

          # 动作1-N：邻居卫星
          elif action <= self.max_neighbors:
              neighbor_idx = action - 1
              visible_neighbors =
  self._get_visible_neighbors(sat_idx, visibility_matrix)

              if neighbor_idx < len(visible_neighbors):
                  target_sat_idx =
  visible_neighbors[neighbor_idx]
                  target_id = f"sat_{target_sat_idx:03d}"

                  # 检查通信质量
                  comm_quality = comm_matrix[sat_idx,
  target_sat_idx] if comm_matrix is not None else 1.0
                  feasible = comm_quality >
  self.config.get('communication', {}).get('min_quality',
  0.1)

                  return TaskAssignment(
                      task_id=task.task_id,
                      source_satellite=agent_id,
                      target_type='neighbor',
                      target_id=target_id,
                      feasible=feasible,

  estimated_cost=self._estimate_transfer_cost(task,
  comm_quality)
                  )
              else:
                  # 无效邻居
                  return TaskAssignment(
                      task_id=task.task_id,
                      source_satellite=agent_id,
                      target_type='invalid',
                      target_id=None,
                      feasible=False,
                      estimated_cost=float('inf')
                  )

          # 动作N+1-M：云处理
          else:
              cloud_idx = action - self.max_neighbors - 1

              # 检查地面站可见性
              if self._check_ground_visibility(sat_idx,
  visibility_matrix):
                  return TaskAssignment(
                      task_id=task.task_id,
                      source_satellite=agent_id,
                      target_type='cloud',
                      target_id=f"cloud_{cloud_idx}",
                      feasible=True,

  estimated_cost=self._estimate_cloud_cost(task)
                  )
              else:
                  return TaskAssignment(
                      task_id=task.task_id,
                      source_satellite=agent_id,
                      target_type='cloud',
                      target_id=f"cloud_{cloud_idx}",
                      feasible=False,
                      estimated_cost=float('inf')
                  )

      def _get_visible_neighbors(self, sat_idx: int,
  visibility_matrix: np.ndarray) -> List[int]:
          """获取可见邻居"""
          if visibility_matrix is None:
              return []
          visible = np.where(visibility_matrix[sat_idx] >
  0)[0]
          return visible.tolist()

      def _check_ground_visibility(self, sat_idx: int,
  visibility_matrix: np.ndarray) -> bool:
          """检查地面站可见性"""
          # 简化：假设有地面站可见性信息
          return True  # TODO: 实现实际检查

      def _estimate_local_cost(self, task) -> float:
          """估算本地处理成本"""
          return task.complexity_cycles_per_bit *
  task.data_size_mb * 0.001

      def _estimate_transfer_cost(self, task, comm_quality:       
  float) -> float:
          """估算传输成本"""
          return task.data_size_mb / (comm_quality + 0.001) *     
   0.01

      def _estimate_cloud_cost(self, task) -> float:
          """估算云处理成本"""
          return task.data_size_mb * 0.0001

      def batch_validate(self,
                         assignments_dict: Dict[str,
  List[TaskAssignment]],
                         global_state: Dict) -> Dict[str,
  List[TaskAssignment]]:
          """
          批量验证所有卫星的分配决策
          处理冲突和资源约束
          """
          validated = {}

          # 统计每个目标的负载
          target_loads = {}

          for agent_id, assignments in
  assignments_dict.items():
              validated[agent_id] = []

              for assignment in assignments:
                  if assignment.feasible:
                      # 检查目标负载
                      if assignment.target_id not in
  target_loads:
                          target_loads[assignment.target_id]      
  = 0

                      # 简单的负载均衡检查
                      if target_loads[assignment.target_id] <     
   self.config.get('rl_env', {}).get('max_target_load', 50):      

  validated[agent_id].append(assignment)
                          target_loads[assignment.target_id]      
  += 1
                      else:
                          # 目标过载，标记为不可行
                          assignment.feasible = False

  validated[agent_id].append(assignment)

          return validated

  2.3 Phase 3: 配置文件更新

  # 在 src/env/physics_layer/config.yaml 中添加

  rl_env:
    # 环境参数
    max_queue_size: 20              #
  任务队列最大长度（用于padding）
    max_target_load: 50             # 单个目标最大负载

    # 奖励权重
    completion_weight: 1.0          # 任务完成奖励权重
    delay_weight: 0.5              # 延迟惩罚权重
    energy_weight: 0.3             # 能耗惩罚权重

    # 动作空间
    max_visible_neighbors: 10       # 最大可见邻居数

    # 模式切换
    use_rl_mode: true              #
  是否使用RL模式（绕过TaskDistributor）
    fallback_to_heuristic: false   # 失败时是否回退到启发式       

  三、集成测试计划

  3.1 单元测试

  # test/env/test_space3_env.py

  def test_observation_padding():
      """测试观测空间的padding和masking"""
      env = Space3Env()
      obs, _ = env.reset()

      for agent_id, agent_obs in obs.items():
          # 验证形状
          assert agent_obs['task_queue'].shape == (20, 8)
          assert agent_obs['task_mask'].shape == (20,)

          # 验证mask
          real_tasks = np.sum(agent_obs['task_mask'])
          assert real_tasks >= 0 and real_tasks <= 20

  def test_action_sequence_execution():
      """测试动作序列执行"""
      env = Space3Env()
      obs, _ = env.reset()

      # 创建测试动作
      actions = {}
      for agent_id in env.agents:
          # 全部本地处理
          actions[agent_id] = np.zeros(20, dtype=int)

      # 执行步骤
      next_obs, rewards, terms, truncs, infos =
  env.step(actions)

      # 验证返回值
      assert len(rewards) == len(env.agents)
      assert all(isinstance(r, float) for r in
  rewards.values())

  def test_rl_mode_bypasses_distributor():
      """验证RL模式绕过TaskDistributor"""
      env = Space3Env()

      # 确保不调用TaskDistributor的分发方法
      # 可以通过mock或日志验证
      pass

  四、实施时间线

  第1周：
  - Day 1-2: 实现space3_env.py基础框架
  - Day 3-4: 完成观测空间构建（含padding/masking）
  - Day 5: 实现动作处理逻辑（替代TaskDistributor）

  第2周：
  - Day 1-2: 实现sequence_action_adapter.py
  - Day 3: 集成奖励计算（基于AlgorithmMetrics）
  - Day 4-5: 测试与调试

  第3周：
  - Day 1-2: 性能优化
  - Day 3: 与Transformer-MAPPO集成测试
  - Day 4-5: 文档和示例编写

  五、关键验证点

  1. Padding正确性：任务队列正确填充到固定长度
  2. Masking有效性：Transformer能识别真实vs填充任务
  3. TaskDistributor绕过：RL决策完全替代原有分配逻辑
  4. 奖励合理性：基于实际性能指标的奖励计算
  5. 性能基准：与原启发式算法对比

  这个详细方案充分考虑了你提出的关键点，确保了实现的正确性和      
  可行性。