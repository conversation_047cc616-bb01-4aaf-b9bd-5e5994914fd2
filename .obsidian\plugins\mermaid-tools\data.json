{"elements": [{"id": "608cddfd-ce48-480f-bc90-50aa4f53f6d5", "categoryId": "flowchart", "description": "a simple flowchart with top to down direction", "content": "flowchart TD\nStart --> Stop", "sortingOrder": 1, "isPinned": false}, {"id": "4746b36b-6e0d-4b66-b387-05cc623c2a91", "categoryId": "flowchart", "description": "a simple flowchart with left to right direction", "content": "flowchart LR\nStart --> Stop", "sortingOrder": 2, "isPinned": false}, {"id": "e5acc818-c05f-4809-a13a-820cf2e899e9", "categoryId": "flowchart", "description": "A node with round edges", "content": "id1(Some text)", "sortingOrder": 3, "isPinned": false}, {"id": "40339c2b-0c93-418c-9cdc-ed4e78a19e0f", "categoryId": "flowchart", "description": "A stadium-shaped node", "content": "id1([Some text])", "sortingOrder": 4, "isPinned": false}, {"id": "4e003e3b-7ccf-4e1c-8e26-615f30ec3ec5", "categoryId": "flowchart", "description": "A node in a cylindrical shape", "content": "id1[(Database)]", "sortingOrder": 5, "isPinned": false}, {"id": "ea570f57-1193-47ff-a4a4-57a3ea71064d", "categoryId": "flowchart", "description": "Circle", "content": "id1((Some text))", "sortingOrder": 6, "isPinned": false}, {"id": "93ec6cfa-1183-4e65-aa1b-713d770432c6", "categoryId": "flowchart", "description": "Rhombus", "content": "id1{Some text}", "sortingOrder": 7, "isPinned": false}, {"id": "eec902a2-f32c-4270-af97-19062d32b9b8", "categoryId": "flowchart", "description": "Hexagon", "content": "id1{{Some text}}", "sortingOrder": 8, "isPinned": false}, {"id": "ea8c70d6-e370-405f-9ccc-e8fcba91c555", "categoryId": "flowchart", "description": "Parallelogram skewed right", "content": "id1[/Some text/]", "sortingOrder": 9, "isPinned": false}, {"id": "a75519a7-9460-4d38-8f7a-04b623ad6be1", "categoryId": "flowchart", "description": "Parallelogram skewed left", "content": "id1[\\Some text\\]", "sortingOrder": 10, "isPinned": false}, {"id": "ced59b67-25c7-490d-92fc-ffe78e5d5e0c", "categoryId": "flowchart", "description": "Trapezoid", "content": "A[/Some text\\]", "sortingOrder": 11, "isPinned": false}, {"id": "77f6adc6-c9e1-4329-98a8-4e69659b46a6", "categoryId": "flowchart", "description": "Trapezoid upside down", "content": "A[\\Some text/]", "sortingOrder": 12, "isPinned": false}, {"id": "acacc45c-64c3-48a2-bc30-024e7f5fd184", "categoryId": "flowchart", "description": "Double circle node", "content": "id1(((Some text)))", "sortingOrder": 13, "isPinned": false}, {"id": "692be040-adb7-461c-a430-65e8be63d762", "categoryId": "flowchart", "description": "A link with arrow head", "content": "A-->B", "sortingOrder": 14, "isPinned": false}, {"id": "71535e54-2ced-45f7-bb4c-ce44d2762ede", "categoryId": "flowchart", "description": "An open link", "content": "A --- B", "sortingOrder": 15, "isPinned": false}, {"id": "1e0513a2-d3bc-4277-a893-930961ebe34a", "categoryId": "flowchart", "description": "Text on links", "content": "A-- This is the text! ---B", "sortingOrder": 16, "isPinned": false}, {"id": "5bb84130-1c5e-416e-8e27-b90423b127c6", "categoryId": "flowchart", "description": "A link with arrow head and text", "content": "A-->|text|B", "sortingOrder": 17, "isPinned": false}, {"id": "5ae80657-3c5d-481f-8e6c-b60c771f7faf", "categoryId": "flowchart", "description": "Dotted link", "content": "A-.->B", "sortingOrder": 18, "isPinned": false}, {"id": "2b1101ad-94c7-4bf2-9c75-b757179039a5", "categoryId": "flowchart", "description": "Thick link", "content": "A ==> B", "sortingOrder": 19, "isPinned": false}, {"id": "792863a7-b2f5-4668-984b-f4282f70e16f", "categoryId": "flowchart", "description": "Invisible link", "content": "A ~~~ B", "sortingOrder": 20, "isPinned": false}, {"id": "eb74a076-f2eb-4a03-a56c-8e6465ca86fd", "categoryId": "flowchart", "description": "Link with circle edge", "content": "A --o B", "sortingOrder": 21, "isPinned": false}, {"id": "481dc03a-0aed-4eec-a70f-b3042203d424", "categoryId": "flowchart", "description": "Link with cross edge", "content": "A --x B", "sortingOrder": 22, "isPinned": false}, {"id": "da45f4d8-3679-4776-83f6-0dd35a68e5f6", "categoryId": "flowchart", "description": "Subgraph", "content": "subgraph one\na1-->a2\nend", "sortingOrder": 14, "isPinned": false}, {"id": "095e8a86-b210-4920-883a-5812512f92e2", "categoryId": "sequenceDiagram", "description": "a simple sequence diagram", "content": "sequenceDiagram\nAlice->>John: Hello <PERSON>, how are you?\nJohn-->>Alice: <PERSON>!\nAlice-)<PERSON>: See you later!", "sortingOrder": 0, "isPinned": false}, {"id": "9855e000-adf4-4ec1-8306-725b4b294c63", "categoryId": "sequenceDiagram", "description": "a simple sequence diagram with actors", "content": "sequenceDiagram\nactor <PERSON>\nactor <PERSON>->>John: Hello <PERSON>, how are you?\nJohn-->><PERSON>: <PERSON>!\nAlice-)<PERSON>: See you later!", "sortingOrder": 1, "isPinned": false}, {"id": "e87c9193-e307-4a64-ab99-5c0dacc2e6eb", "categoryId": "classDiagram", "description": "sample class", "content": "class Duck{\n            +String beakColor\n            +swim()\n            +quack()\n        }", "sortingOrder": 0, "isPinned": false}, {"id": "bdff34d3-d3d8-4dd3-8f38-6db8d20fa9a5", "categoryId": "classDiagram", "description": "sample class", "content": "class BankAccount\n        BankAccount : +String owner\n        BankAccount : +Bigdecimal balance\n        BankAccount : +deposit(amount)\n        BankAccount : +withdrawal(amount)", "sortingOrder": 1, "isPinned": false}, {"id": "b245eac3-97fc-43d5-8dbf-82eada94e68e", "categoryId": "classDiagram", "description": "generic class", "content": "class Square~Shape~{\n            int id\n            List~int~ position\n            setPoints(List~int~ points)\n            getPoints() List~int~\n        }\n        \n        Square : -List~string~ messages\n        Square : +setMessages(List~string~ messages)\n        Square : +getMessages() List~string~", "sortingOrder": 2, "isPinned": false}, {"id": "14c7d719-2caa-4bd2-9f33-cddde792daaa", "categoryId": "classDiagram", "description": "inheritance", "content": "classA <|-- classB", "sortingOrder": 3, "isPinned": false}, {"id": "cc51fe53-edb9-49d5-8c80-fb97263e1245", "categoryId": "classDiagram", "description": "composition", "content": "classC *-- classD", "sortingOrder": 4, "isPinned": false}, {"id": "efeef077-86d4-4a3e-a7e0-480f4b3fee96", "categoryId": "classDiagram", "description": "aggregation", "content": "classE o-- classF", "sortingOrder": 5, "isPinned": false}, {"id": "9ffeecec-7c65-4126-a5fa-80540eb588e5", "categoryId": "classDiagram", "description": "association", "content": "classG <-- classH", "sortingOrder": 6, "isPinned": false}, {"id": "97fde0df-684e-4a37-b164-861b6dd77844", "categoryId": "classDiagram", "description": "solid link", "content": "classI -- classJ", "sortingOrder": 7, "isPinned": false}, {"id": "dde50007-87a2-4713-96c7-fc47ba6e1e83", "categoryId": "classDiagram", "description": "dependency", "content": "classK <.. classL", "sortingOrder": 8, "isPinned": false}, {"id": "01e68bf6-bc7f-4b5e-85b8-7f0957a8ad22", "categoryId": "classDiagram", "description": "realization", "content": "classM <|.. classN", "sortingOrder": 9, "isPinned": false}, {"id": "0ee053bf-12a8-42d3-aa2c-e8721dd36f25", "categoryId": "classDiagram", "description": "dashed link", "content": "classO .. classP", "sortingOrder": 10, "isPinned": false}, {"id": "0f539aec-6d10-47dd-ae68-29a25af4ce6f", "categoryId": "classDiagram", "description": "two-way relation", "content": "Animal <|--|> Zebra", "sortingOrder": 11, "isPinned": false}, {"id": "6e8e901e-6749-458b-a3ab-3f5566464e2f", "categoryId": "classDiagram", "description": "sample class diagram", "content": "classDiagram\n        Animal <|-- <PERSON>\n        Animal <|-- <PERSON>\n        Animal <|-- Zebra\n        Animal : +int age\n        Animal : +String gender\n        Animal: +isMammal()\n        Animal: +mate()\n        class Duck{\n            +String beakColor\n            +swim()\n            +quack()\n        }\n        class Fish{\n            -int sizeInFeet\n            -canEat()\n        }\n        class Zebra{\n            +bool is_wild\n            +run()\n        }", "sortingOrder": 12, "isPinned": false}, {"id": "f79e46c8-ef5f-4573-82cf-2855e0bcc503", "categoryId": "stateDiagram", "description": "a sample state diagram", "content": "stateDiagram-v2\n        [*] --> Still\n        Still --> [*]\n    \n        Still --> Moving\n        Moving --> Still\n        Moving --> Crash\n        Crash --> [*]", "sortingOrder": 0, "isPinned": false}, {"id": "ffc82176-8382-4ecd-91c7-c0efdb21743c", "categoryId": "stateDiagram", "description": "a sample state diagram with left-to-right direction", "content": "stateDiagram-v2\n        direction LR\n        [*] --> Still\n        Still --> [*]\n    \n        Still --> Moving\n        Moving --> Still\n        Moving --> Crash\n        Crash --> [*]", "sortingOrder": 1, "isPinned": false}, {"id": "fa360dd1-ac63-4a94-bb1f-1bd484868a00", "categoryId": "stateDiagram", "description": "node with description", "content": "s2 : This is a state description", "sortingOrder": 2, "isPinned": false}, {"id": "bd4462b2-c9b0-445d-ae75-5ce5cadb8480", "categoryId": "stateDiagram", "description": "a transition", "content": "s1 --> s2", "sortingOrder": 3, "isPinned": false}, {"id": "2df0f957-3a4f-4b0e-bed9-823938a4c1fb", "categoryId": "stateDiagram", "description": "a transition with label", "content": "s1 --> s2: A transition", "sortingOrder": 4, "isPinned": false}, {"id": "aac672f2-514e-436e-b9c9-06283ea7a63e", "categoryId": "stateDiagram", "description": "composite state", "content": "\n        [*] --> First\n        state First {\n            [*] --> second\n            second --> [*]\n        }", "sortingOrder": 5, "isPinned": false}, {"id": "9526280b-9326-45c4-862d-7c3b5150c9ff", "categoryId": "stateDiagram", "description": "diagram with choice", "content": "stateDiagram-v2\n        state if_state <<choice>>\n        [*] --> IsPositive\n        IsPositive --> if_state\n        if_state --> False: if n < 0\n        if_state --> True : if n >= 0", "sortingOrder": 6, "isPinned": false}, {"id": "d6ebbf7c-58c0-42bf-86b7-8192158c27b5", "categoryId": "stateDiagram", "description": "diagram with fork", "content": "stateDiagram-v2\n        state fork_state <<fork>>\n          [*] --> fork_state\n          fork_state --> State2\n          fork_state --> State3\n    \n          state join_state <<join>>\n          State2 --> join_state\n          State3 --> join_state\n          join_state --> State4\n          State4 --> [*]", "sortingOrder": 7, "isPinned": false}, {"id": "5b275f96-7b02-470d-897a-1ed70a68a345", "categoryId": "stateDiagram", "description": "a diagram with concurrency", "content": "stateDiagram-v2\n        [*] --> Active\n    \n        state Active {\n            [*] --> NumLockOff\n            NumLockOff --> NumLockOn : EvNumLockPressed\n            NumLockOn --> NumLockOff : EvNumLockPressed\n            --\n            [*] --> CapsLockOff\n            CapsLockOff --> CapsLockOn : EvCapsLockPressed\n            CapsLockOn --> CapsLockOff : EvCapsLockPressed\n            --\n            [*] --> ScrollLockOff\n            ScrollLockOff --> ScrollLockOn : EvScrollLockPressed\n            ScrollLockOn --> ScrollLockOff : EvScrollLockPressed\n        }", "sortingOrder": 8, "isPinned": false}, {"id": "ef07de6c-cd0d-4b81-b6fd-7371197e07ab", "categoryId": "entityRelationshipDiagram", "description": "a sample entity relationship diagram", "content": "erDiagram\n        CUSTOMER ||--o{ ORDER : places\n        ORDER ||--|{ LINE-ITEM : contains\n        CUSTOMER }|..|{ DELIVERY-ADDRESS : uses", "sortingOrder": 0, "isPinned": false}, {"id": "66ec7f9a-18a7-433a-994b-154c0727160e", "categoryId": "entityRelationshipDiagram", "description": "an entity", "content": "    CUSTOMER {\n            string name\n            string custNumber\n            string sector\n        }", "sortingOrder": 1, "isPinned": false}, {"id": "06d13b91-ec11-4f24-bc67-d38174fda0d5", "categoryId": "entityRelationshipDiagram", "description": "one-to-many relationship", "content": "A ||--|{ B : label", "sortingOrder": 2, "isPinned": false}, {"id": "3c1ba044-aeb2-4ba4-8f22-0aa262a51d19", "categoryId": "entityRelationshipDiagram", "description": "many-to-many relationship", "content": "A }|--|{ B : label", "sortingOrder": 3, "isPinned": false}, {"id": "ffcbc14a-7e3a-4f20-aca2-0eba6757852f", "categoryId": "entityRelationshipDiagram", "description": "one-to-one relationship", "content": "A ||--|| B : label", "sortingOrder": 4, "isPinned": false}, {"id": "56326d65-1d46-4170-aa75-17d8e8e2254c", "categoryId": "entityRelationshipDiagram", "description": "many-to-one relationship", "content": "A }|--|| B : label", "sortingOrder": 5, "isPinned": false}, {"id": "2f23abf4-4522-4641-9c56-a93d39e63eb5", "categoryId": "entityRelationshipDiagram", "description": "zero/one-to-one relationship", "content": "A |o--|| B : label", "sortingOrder": 6, "isPinned": false}, {"id": "b5879d17-8d66-4b68-99d5-359357229446", "categoryId": "entityRelationshipDiagram", "description": "one-to-one/zero relationship", "content": "A ||--o| B : label", "sortingOrder": 7, "isPinned": false}, {"id": "eeab4d55-eb51-44f6-be1c-aa6d82b56b09", "categoryId": "entityRelationshipDiagram", "description": "zero-or-more-to-one relationship", "content": "A }o--|| B : label", "sortingOrder": 8, "isPinned": false}, {"id": "5c2f0eb3-4fc0-4ae6-8be1-eaae66d3a783", "categoryId": "entityRelationshipDiagram", "description": "one-to-zero-or-more relationship", "content": "A ||--o{ B : label", "sortingOrder": 9, "isPinned": false}, {"id": "32aabbfe-5d88-4ac7-8742-db1ae77c8135", "categoryId": "entityRelationshipDiagram", "description": "zero-or-more-to-many relationship", "content": "A }o--|{ B : label", "sortingOrder": 10, "isPinned": false}, {"id": "c18c264e-b6f4-43e6-8696-b752753bb84d", "categoryId": "entityRelationshipDiagram", "description": "many-to-zero-or-more relationship", "content": "A }|--o{ B : label", "sortingOrder": 11, "isPinned": false}, {"id": "8808c201-48c5-4039-8127-91e539473597", "categoryId": "userJourneyDiagram", "description": "a sample user journey diagram", "content": "journey\n        title My working day\n        section Go to work\n          Make tea: 5: Me\n          Go upstairs: 3: Me\n          Do work: 1: Me, Cat\n        section Go home\n          Go downstairs: 5: Me\n          Sit down: 5: Me", "sortingOrder": 0, "isPinned": false}, {"id": "891c33e1-f3db-4189-9a0e-cb29adab99c8", "categoryId": "userJourneyDiagram", "description": "a step in user journey", "content": "      Step Title: 5: <PERSON><PERSON><PERSON>", "sortingOrder": 1, "isPinned": false}, {"id": "a799aceb-3373-4c54-8167-be3a44e532e7", "categoryId": "ganttChart", "description": "simple gantt chart", "content": "gantt\n        title A Gantt Diagram\n        dateFormat  YYYY-MM-DD\n        section Section\n        A task           :a1, 2014-01-01, 30d\n        Another task     :after a1  , 20d\n        section Another\n        Task in sec      :2014-01-12  , 12d\n        another task      : 24d", "sortingOrder": 0, "isPinned": false}, {"id": "bee58d6c-4d3c-40ab-a792-578fbce036da", "categoryId": "ganttChart", "description": "rich gantt chart", "content": "gantt\n        dateFormat  YYYY-MM-DD\n        title       Adding GANTT diagram functionality to mermaid\n        excludes    weekends\n    \n        section A section\n        Completed task            :done,    des1, 2014-01-06,2014-01-08\n        Active task               :active,  des2, 2014-01-09, 3d\n        Future task               :         des3, after des2, 5d\n        Future task2              :         des4, after des3, 5d\n    \n        section Critical tasks\n        Completed task in the critical line :crit, done, 2014-01-06,24h\n        Implement parser and jison          :crit, done, after des1, 2d\n        Create tests for parser             :crit, active, 3d\n        Future task in critical line        :crit, 5d\n        Create tests for renderer           :2d\n        Add to mermaid                      :1d\n        Functionality added                 :milestone, 2014-01-25, 0d\n    \n        section Documentation\n        Describe gantt syntax               :active, a1, after des1, 3d\n        Add gantt diagram to demo page      :after a1  , 20h\n        Add another diagram to demo page    :doc1, after a1  , 48h\n    \n        section Last section\n        Describe gantt syntax               :after doc1, 3d\n        Add gantt diagram to demo page      :20h\n        Add another diagram to demo page    :48h", "sortingOrder": 1, "isPinned": false}, {"id": "55a76893-66c5-463a-9f72-ef04943aed6d", "categoryId": "ganttChart", "description": "milestones example", "content": "gantt\n        dateFormat HH:mm\n        axisFormat %H:%M\n        Initial milestone : milestone, m1, 17:49,2min\n        taska2 : 10min\n        taska3 : 5min\n        Final milestone : milestone, m2, 18:14, 2min", "sortingOrder": 2, "isPinned": false}, {"id": "810218de-5409-4078-83be-32965f5fe5b8", "categoryId": "<PERSON><PERSON><PERSON>", "description": "sample pie chart", "content": "pie title /r/obsidianmd posts by type\n        \"Graphs\" : 85\n        \"Dashboards\" : 14\n        \"Tips\" : 1", "sortingOrder": 0, "isPinned": false}, {"id": "c9e1ee52-7fca-4a99-b319-7b856ffb2ca6", "categoryId": "<PERSON><PERSON><PERSON>", "description": "sample pie chart with values shown in legend", "content": "pie showData title /r/obsidianmd posts by type\n        \"Graphs\" : 85\n        \"Dashboards\" : 14\n        \"Tips\" : 1", "sortingOrder": 1, "isPinned": false}, {"id": "eba868c5-b924-4ee1-86b1-4a9ca1735e58", "categoryId": "requirementDiagram", "description": "sample requirements diagram", "content": "    requirementDiagram\n\n        requirement test_req {\n        id: 1\n        text: the test text.\n        risk: high\n        verifymethod: test\n        }\n    \n        element test_entity {\n        type: simulation\n        }\n    \n        test_entity - satisfies -> test_req", "sortingOrder": 0, "isPinned": false}, {"id": "5cda249a-a0b6-4cc6-8c99-b5a2b7ceb9ed", "categoryId": "requirementDiagram", "description": "sample requirements diagram", "content": "element customElement {\n            type: customType\n            docref: customDocRef\n        }", "sortingOrder": 1, "isPinned": false}, {"id": "66d16050-8d77-4cd2-aaaa-877c66350d16", "categoryId": "requirementDiagram", "description": "a requirement with high risk", "content": "functionalRequirement myReq {\n            id: reqId\n            text: someText\n            risk: High\n            verifymethod: analysis\n        }", "sortingOrder": 2, "isPinned": false}, {"id": "00d93060-910b-41e0-a3f3-8f307d38db2f", "categoryId": "requirementDiagram", "description": "sample requirements diagram", "content": "interfaceRequirement myReq2 {\n            id: reqId\n            text: someText\n            risk: Medium\n            verifymethod: demonstration\n        }", "sortingOrder": 3, "isPinned": false}, {"id": "46ea4fbe-b9f7-4a33-8c5b-eb38ff66a4ae", "categoryId": "requirementDiagram", "description": "sample requirements diagram", "content": "designConstraint myReq3 {\n            id: reqId\n            text: someText\n            risk: Low\n            verifymethod: test\n        }", "sortingOrder": 4, "isPinned": false}, {"id": "d19842e6-6841-4153-b95b-91c54fbc4786", "categoryId": "gitGraph", "description": "simple git graph", "content": "gitGraph\n        commit\n        commit\n        branch develop\n        checkout develop\n        commit\n        commit\n        checkout main\n        merge develop\n        commit\n        commit", "sortingOrder": 0, "isPinned": false}, {"id": "eb6876db-79a5-4a19-9f26-6711ef5c734a", "categoryId": "gitGraph", "description": "tagged commit", "content": "commit id: \"Normal\" tag: \"v1.0.0\"", "sortingOrder": 1, "isPinned": false}, {"id": "92f9739e-bb09-425f-966f-400eda2eb6e9", "categoryId": "gitGraph", "description": "reverse commit", "content": "commit id: \"Reverse\" type: REVERSE", "sortingOrder": 2, "isPinned": false}, {"id": "6f5cf487-539a-49bf-9610-c9f3323a6178", "categoryId": "gitGraph", "description": "highlighted commit", "content": "commit id: \"Highlight\" type: HIGHLIGHT", "sortingOrder": 3, "isPinned": false}, {"id": "99dd7853-70e0-41ff-8f5f-6846944f0787", "categoryId": "gitGraph", "description": "reverse commit", "content": "commit id: \"Reverse\" type: REVERSE", "sortingOrder": 4, "isPinned": false}, {"id": "ca8a467a-9724-45f8-8604-26c8275f00c1", "categoryId": "gitGraph", "description": "git graph with cherry-pick", "content": "gitGraph\n        commit id: \"ZERO\"\n        branch develop\n        commit id:\"A\"\n        checkout main\n        commit id:\"ONE\"\n        checkout develop\n        commit id:\"B\"\n        checkout main\n        commit id:\"TWO\"\n        cherry-pick id:\"A\"\n        commit id:\"THREE\"\n        checkout develop\n        commit id:\"C\"", "sortingOrder": 5, "isPinned": false}, {"id": "96b3ad73-90c4-48b0-80ae-080e0466587b", "categoryId": "mindmap", "description": "a simple mindmap", "content": "mindmap\n        Root\n            A\n              B\n              C", "sortingOrder": 1, "isPinned": false}, {"id": "32ed44b0-7bf7-47de-b56b-05f6b278c9fc", "categoryId": "mindmap", "description": "square", "content": "id[I am a square]", "sortingOrder": 2, "isPinned": false}, {"id": "4f4379bd-f89c-4cfb-8ded-1e54941c79da", "categoryId": "mindmap", "description": "rounded square", "content": "id(I am a rounded square)", "sortingOrder": 3, "isPinned": false}, {"id": "c2a82c7a-7fc8-40dd-99e0-f26c7e384bb0", "categoryId": "mindmap", "description": "circle", "content": "id((I am a circle))", "sortingOrder": 4, "isPinned": false}, {"id": "030cdae7-bc4e-4132-bbe0-c6607365ad77", "categoryId": "mindmap", "description": "bang", "content": "id))I am a bang((", "sortingOrder": 5, "isPinned": false}, {"id": "2cd9c9ec-f4ae-40eb-b836-38355c71b0b4", "categoryId": "mindmap", "description": "cloud", "content": "id)I am a cloud(", "sortingOrder": 6, "isPinned": false}, {"id": "60761c55-6631-46c3-a1e7-8fd899eea258", "categoryId": "mindmap", "description": "hexagon", "content": "id{{I am a hexagon}}", "sortingOrder": 7, "isPinned": false}, {"id": "fe59f092-ee53-4464-b1c5-7e31f43e41e7", "categoryId": "mindmap", "description": "default", "content": "I am the default shape", "sortingOrder": 8, "isPinned": false}, {"id": "870cea37-501e-4739-adc1-9362d54ef110", "categoryId": "mindmap", "description": "sample mindmap", "content": "mindmap\n        root((mindmap))\n          Origins\n            Long history\n            Popularisation\n              British popular psychology author <PERSON>\n          Research\n            On effectiveness<br/>and features\n            On Automatic creation\n              Uses\n                  Creative techniques\n                  Strategic planning\n                  Argument mapping\n          Tools\n            Pen and paper\n            Mermaid", "sortingOrder": 9, "isPinned": false}, {"id": "5ab7deb5-5d0d-4fb1-8154-62ca3aea9818", "categoryId": "timeline", "description": "sample timeline", "content": "timeline\n\t\ttitle History of Social Media Platform\n\t\t2002 : LinkedIn\n\t\t2004 : Facebook\n\t\t\t : Google\n\t\t2005 : Youtube\n\t\t2006 : Twitter", "sortingOrder": 1, "isPinned": false}, {"id": "d5898ea9-59fb-4aa3-8fdd-4c03f5fe79f0", "categoryId": "timeline", "description": "timeline with grouping", "content": "timeline\n\t\ttitle Timeline of Industrial Revolution\n\t\tsection 17th-20th century\n\t\t\tIndustry 1.0 : Machinery, Water power, Steam <br>power\n\t\t\tIndustry 2.0 : Electricity, Internal combustion engine, Mass production\n\t\t\tIndustry 3.0 : Electronics, Computers, Automation\n\t\tsection 21st century\n\t\t\tIndustry 4.0 : Internet, Robotics, Internet of Things\n\t\t\tIndustry 5.0 : Artificial intelligence, Big data,3D printing", "sortingOrder": 2, "isPinned": false}, {"id": "111ca45d-6437-41e6-9a04-9ef4daff5494", "categoryId": "timeline", "description": "timeline with Forest theme. see the docs for additional themes", "content": "%%{init: { 'logLevel': 'debug', 'theme': 'forest' } }%%\n\t\ttimeline\n\t\t\ttitle History of Social Media Platform\n\t\t\t  2002 : LinkedIn\n\t\t\t  2004 : Facebook : Google\n\t\t\t  2005 : Youtube\n\t\t\t  2006 : Twitter\n\t\t\t  2007 : Tumblr\n\t\t\t  2008 : Instagram\n\t\t\t  2010 : Pinterest", "sortingOrder": 3, "isPinned": false}, {"id": "914a2123-68c9-4976-9c4c-d2a60ec562f5", "categoryId": "quadrantChart", "description": "sample quadrant chart", "content": "quadrantChart\n\t\ttitle Reach and engagement of campaigns\n\t\tx-axis Low Reach --> High Reach\n\t\ty-axis Low Engagement --> High Engagement\n\t\tquadrant-1 We should expand\n\t\tquadrant-2 Need to promote\n\t\tquadrant-3 Re-evaluate\n\t\tquadrant-4 May be improved\n\t\tCampaign A: [0.3, 0.6]\n\t\tCampaign B: [0.45, 0.23]\n\t\tCampaign C: [0.57, 0.69]\n\t\tCampaign D: [0.78, 0.34]\n\t\tCampaign E: [0.40, 0.34]\n\t\tCampaign F: [0.35, 0.78]", "sortingOrder": 1, "isPinned": false}, {"id": "7e4493c9-2aab-4ebd-b270-ad126ce4037e", "categoryId": "quadrantChart", "description": "themed quadrant chart", "content": "%%{init: {\"quadrantChart\": {\"chartWidth\": 400, \"chartHeight\": 400}, \"themeVariables\": {\"quadrant1TextFill\": \"#ff0000\"} }}%%\n\t\tquadrantChart\n\t\t  x-axis Urgent --> Not Urgent\n\t\t  y-axis Not Important --> \"Important ❤\"\n\t\t  quadrant-1 Plan\n\t\t  quadrant-2 Do\n\t\t  quadrant-3 Delegate\n\t\t  quadrant-4 Delete", "sortingOrder": 1, "isPinned": false}, {"id": "8d80e1ea-42aa-44fc-846e-b0a1bda30481", "categoryId": "c4Diagram", "description": "sample C4 diagram (compatible with PlantUML)", "content": "C4Context\n\t\ttitle System Context diagram for Internet Banking System\n\t\tEnterprise_Boundary(b0, \"BankBoundary0\") {\n\t\t  Person(customerA, \"Banking Customer A\", \"A customer of the bank, with personal bank accounts.\")\n\t\t  Person(customerB, \"Banking Customer B\")\n\t\t  Person_Ext(customerC, \"Banking Customer C\", \"desc\")\n  \n\t\t  Person(customerD, \"Banking Customer D\", \"A customer of the bank, <br/> with personal bank accounts.\")\n  \n\t\t  System(SystemAA, \"Internet Banking System\", \"Allows customers to view information about their bank accounts, and make payments.\")\n  \n\t\t  Enterprise_Boundary(b1, \"BankBoundary\") {\n  \n\t\t\tSystemDb_Ext(SystemE, \"Mainframe Banking System\", \"Stores all of the core banking information about customers, accounts, transactions, etc.\")\n  \n\t\t\tSystem_Boundary(b2, \"BankBoundary2\") {\n\t\t\t  System(SystemA, \"Banking System A\")\n\t\t\t  System(SystemB, \"Banking System B\", \"A system of the bank, with personal bank accounts. next line.\")\n\t\t\t}\n  \n\t\t\tSystem_Ext(SystemC, \"E-mail system\", \"The internal Microsoft Exchange e-mail system.\")\n\t\t\tSystemDb(SystemD, \"Banking System D Database\", \"A system of the bank, with personal bank accounts.\")\n  \n\t\t\tBoundary(b3, \"BankBoundary3\", \"boundary\") {\n\t\t\t  SystemQueue(SystemF, \"Banking System F Queue\", \"A system of the bank.\")\n\t\t\t  SystemQueue_Ext(SystemG, \"Banking System G Queue\", \"A system of the bank, with personal bank accounts.\")\n\t\t\t}\n\t\t  }\n\t\t}\n  \n\t\tBiRel(customerA, SystemAA, \"Uses\")\n\t\tBiRel(SystemAA, SystemE, \"Uses\")\n\t\tRel(SystemAA, SystemC, \"Sends e-mails\", \"SMTP\")\n\t\tRel(SystemC, customerA, \"Sends e-mails to\")\n  \n\t\tUpdateElementStyle(customerA, $fontColor=\"red\", $bgColor=\"grey\", $borderColor=\"red\")\n\t\tUpdateRelStyle(customerA, SystemAA, $textColor=\"blue\", $lineColor=\"blue\", $offsetX=\"5\")\n\t\tUpdateRelStyle(SystemAA, SystemE, $textColor=\"blue\", $lineColor=\"blue\", $offsetY=\"-10\")\n\t\tUpdateRelStyle(SystemAA, SystemC, $textColor=\"blue\", $lineColor=\"blue\", $offsetY=\"-40\", $offsetX=\"-50\")\n\t\tUpdateRelStyle(SystemC, customerA, $textColor=\"red\", $lineColor=\"red\", $offsetX=\"-50\", $offsetY=\"20\")\n  \n\t\tUpdateLayoutConfig($c4ShapeInRow=\"3\", $c4BoundaryInRow=\"1\")", "sortingOrder": 1, "isPinned": false}, {"id": "38a874a4-143f-4043-895c-ac2dd3161672", "categoryId": "sankeyDiagram", "description": "", "content": "sankey-beta\n        %% source,target,value\n        Electricity grid,Over generation / exports,104.453\n        Electricity grid,Heating and cooling - homes,113.726\n        Electricity grid,H2 conversion,27.14", "sortingOrder": 0, "isPinned": false}, {"id": "37304fb1-ffcd-45f3-a6a1-599ef72f57d2", "categoryId": "xyChart", "description": "a sample XYChart diagram", "content": "xychart-beta\n        title \"Sales Revenue\"\n        x-axis [jan, feb, mar, apr, may, jun, jul, aug, sep, oct, nov, dec]\n        y-axis \"Revenue (in $)\" 4000 --> 11000\n        bar [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]\n        line [5000, 6000, 7500, 8200, 9500, 10500, 11000, 10200, 9200, 8500, 7000, 6000]", "sortingOrder": 0, "isPinned": false}, {"id": "d8e2ee06-4a60-44ec-a37f-8b0dae84c6df", "categoryId": "packet", "description": "a sample packet diagram", "content": "packet-beta\ntitle UDP Packet\n0-15: \"Source Port\"\n16-31: \"Destination Port\"\n32-47: \"Length\"\n48-63: \"Checksum\"\n64-95: \"Data (variable length)\"\n", "sortingOrder": 0, "isPinned": false}, {"id": "536b95be-05c7-4f69-8d3a-e7c53aeae285", "categoryId": "kanban", "description": "a sample kanban diagram", "content": "kanban\n  <PERSON>\n    [Create Documentation]\n    docs[Create Blog about the new diagram]\n  [In progress]\n    id6[Create renderer so that it works in all cases. We also add som extra text here for testing purposes. And some more just for the extra flare.]\n  id9[Ready for deploy]\n    id8[Design grammar]@{ assigned: 'knsv' }\n  id10[Ready for test]\n    id4[Create parsing tests]@{ ticket: MC-2038, assigned: '<PERSON><PERSON>', priority: 'High' }\n    id66[last item]@{ priority: 'Very Low', assigned: 'knsv' }\n  id11[Done]\n    id5[define getData]\n    id2[Title of diagram is more than 100 chars when user duplicates diagram with 100 char]@{ ticket: MC-2036, priority: 'Very High'}\n    id3[Update DB function]@{ ticket: MC-2037, assigned: knsv, priority: 'High' }\n\n  id12[Can't reproduce]\n    id3[Weird flickering in Firefox]", "sortingOrder": 0, "isPinned": false}, {"id": "168f7306-9924-40f7-9b37-0040f595876c", "categoryId": "block", "description": "a sample block diagram", "content": "block-beta\ncolumns 1\n  db((\"DB\"))\n  blockArrowId6<[\"&nbsp;&nbsp;&nbsp;\"]>(down)\n  block:ID\n    A\n    B[\"A wide one in the middle\"]\n    C\n  end\n  space\n  D\n  ID --> D\n  C --> D\n  style B fill:#969,stroke:#333,stroke-width:4px\n", "sortingOrder": 0, "isPinned": false}, {"id": "ebcbb211-c0f9-4479-afca-b470ed953491", "categoryId": "architecture", "description": "a sample architecture diagram", "content": "architecture-beta\n    group api(cloud)[API]\n\n    service db(database)[Database] in api\n    service disk1(disk)[Storage] in api\n    service disk2(disk)[Storage] in api\n    service server(server)[Server] in api\n\n    db:L -- R:server\n    disk1:T -- B:server\n    disk2:T -- B:db\n", "sortingOrder": 0, "isPinned": false}], "customCategories": [], "selectedCategoryId": "packet", "defaultCategorySortOrders": {}, "categoryModifications": {}}