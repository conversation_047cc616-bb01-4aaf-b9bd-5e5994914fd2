
### **文档整理摘要**

本文档主要阐述了两个核心部分：

1. **一个高阶解决方案**：提出构建一个**基于Transformer的生成式动作空间**，将智能体的单步决策升级为生成完整的行动计划，以解决批量任务处理的难题。
    
2. **一个详细的理论框架设计**：基于**Dec-POMDP**（分布式部分可观测马尔可夫决策过程）模型，详细设计了一个用于模拟LEO（低地球轨道）卫星任务调度的**PettingZoo并行多智能体强化学习环境**。
    

以下是整理后的详细内容：

---

### **第一部分：解决方案核心思想：基于Transformer的生成式动作空间**

为从根本上解决“单步决策”与“批量任务处理”之间的矛盾，我们提出一种核心解决方案：**构建一个生成式的动作空间**，将智能体的决策过程从“选择”一个动作，升级为“生成”一个完整的行动计划。

#### **1.1 核心思想：从“选择”到“生成”**

智能体的动作不再是从一个预定义的、有限的动作集合中进行选择，而是利用一个强大的生成模型，根据当前状态动态地构建一个结构化的、可变长度的行动序列。在这个序列中，每一个元素都对应着对一个具体任务的精细化决策。

#### **1.2 Transformer的角色：序列化的决策大脑**

Transformer模型因其强大的序列处理能力和注意力机制，是实现这一生成式动作空间的理想选择。在我们的框架中，**Transformer本身就是MAPPO智能体的策略网络 `$\pi(a|s)$`**。

- **输入 (Input)**：完整的环境状态，包括卫星自身状态、邻居状态，以及一个包含了**所有待决策任务信息的序列**。
    
- **输出 (Output)**：一个**动作序列**。这个序列的长度与待决策的任务数量相对应，序列中的每个元素都是一个决策向量，详细说明了如何处理对应的任务。
    
- **注意力机制 (Attention Mechanism)**：在为特定任务（如“任务A”）做决策时，注意力机制能够动态权衡所有输入信息的重要性，不仅关注“任务A”的属性，还会同时考虑“任务B”的存在、邻居卫星的负载、以及来自GEO（地球同步轨道）层的全局指导，从而做出全局最优的、上下文感知的决策。
    

---

### **第二部分：PettingZoo并行环境理论框架设计**

基于Dec-POMDP理论模型，我们设计一个适配PettingZoo Parallel API的分布式LEO卫星任务调度环境。

#### **2.1 理论基础：从Dec-POMDP到PettingZoo的映射**

我们将Dec-POMDP的六元组理论模型精确映射到PettingZoo Parallel API规范：

- **智能体集合**: `$\mathcal{L} \rightarrow$ agents`
    
- **状态空间 `$\mathcal{S}$`**: `$\rightarrow$ state` (环境内部全局状态)
    
- **动作空间 `$\mathcal{A}_j$`**: `$\rightarrow$ action_spaces[agent_j]`
    
- **观测空间 `$\Omega_j$`**: `$\rightarrow$ observation_spaces[agent_j]`
    
- **观测函数 `$\mathcal{O}$`**: `$\rightarrow$ observe(agent_j)`
    
- **状态转移函数 `$\mathcal{P}$`**: `$\rightarrow$ step()`
    
- **奖励函数 `$\mathcal{R}_j$`**: `$\rightarrow$ rewards[agent_j]`
    

#### **2.2 环境架构 (`LEOSatelliteParallelEnv`)**

Python

```
class LEOSatelliteParallelEnv(ParallelEnv):
    """
    分布式LEO卫星任务调度环境
    实现Dec-POMDP在PettingZoo框架下的并行多智能体环境
    """
    metadata = {
        "render_modes": ["human", "ansi"],
        "name": "leo_satellite_scheduling_v0",
        "is_parallelizable": True
    }

    def __init__(self, config_path: str):
        # 1. 初始化核心组件 (时间、轨道、通信、任务生成器)
        self.time_manager = TimeManager(config)
        self.orbital_updater = OrbitalUpdater()
        self.communication_manager = CommunicationManager()
        self.task_generator = TaskGenerator()

        # 2. 初始化智能体集合
        self.agents = [f"satellite_{i}" for i in range(72)]
        self.possible_agents = self.agents[:]

        # 3. 定义观测与动作空间
        self._define_spaces()

        # 4. 初始化环境状态
        self._reset_state()

    def reset(self, seed=None, options=None):
        # ...

    def step(self, actions):
        # ...
```

- **环境生命周期**:
    
    1. **初始化 (`__init__` & `reset`)**: 加载轨道数据，初始化全局状态和所有智能体的初始观测。
        
    2. **决策 (`step`)**: 接收所有智能体的并行动作字典，执行状态转移，并返回新的观测、奖励等信息。
        
    3. **终止**: 达到最大时隙、所有任务完成或系统资源耗尽。
        

#### **2.3 观测空间 (Observation Space) 设计**

基于 `_define_observation_space()` 设计一个结构化的、分层的观测空间 `spaces.Dict`，包含以下五个维度：

1. **自身状态 (`self_state`/`local_state`)**: 位置、剩余能量、CPU利用率、任务队列状态等。
    
2. **邻居状态 (`neighbor_states`)**: 可通信邻居的聚合状态信息（部分可观测）。
    
3. **任务特征 (`regional_tasks`/`task_features`)**: 区域内的任务分布、到达率、负载，或队列中具体任务的特征。
    
4. **通信链路 (`link_quality`/`communication_matrix`)**: 与邻居的链路质量。
    
5. **时间上下文 (`time_context`)**: 归一化的时隙、仿真时间等。
    

_设计考虑_：所有观测值均进行归一化处理；使用填充（Padding）和掩码（Masking）来处理可变数量的邻居和任务，以保持维度固定。

#### **2.4 动作空间 (Action Space) 设计**

基于复合动作 `$a_j(t) = \langle \mathbf{D}_j(t), \mathbf{P}_j(t) \rangle$`，设计一个结构化的混合动作空间 `spaces.Dict`：

1. **卸载决策 (`offloading_decisions`)**: 一个离散决策向量 (`spaces.MultiDiscrete`)，为队列中的每个任务选择一个卸载目标（如：0=本地处理, 1-N=邻居卫星, N+1=地面云）。
    
2. **资源分配 (`resource_allocation`)**: 一个连续决策向量 (`spaces.Box`)，为决定在本地处理的每个任务分配计算资源（如CPU比例）。
    
3. **动作掩码 (`action_mask`)**: （可选）用于标识队列中哪些任务是有效的，需要为其做出决策。
    

#### **2.5 智能体交互与状态转移**

- **并行执行框架**: `step` 函数采用三阶段处理流程：
    
    1. **任务卸载阶段**: 根据卸载决策，处理所有跨卫星的任务迁移请求，并考虑通信约束（可见性、带宽、延迟）。
        
    2. **资源分配阶段**: 对确定在本地处理的任务，根据资源分配向量执行资源调度。
        
    3. **任务处理与状态更新阶段**: 模拟任务的实际处理过程，更新任务状态。
        
- **状态转移逻辑 (`_update_global_state`)**:
    
    - **轨道动力学**: 确定性更新卫星位置。
        
    - **能量模型**: 根据计算和通信活动计算能耗，并考虑太阳能充电。
        
    - **任务队列**: 移除已完成/迁移的任务，并根据随机模型（如泊松过程）生成新任务。
        
    - **通信拓扑**: 基于更新后的卫星位置，重新计算卫星间的可见性和链路质量。
        
- **关键算法**:
    
    - **冲突解决**: 当多个任务请求迁移到同一目标时，基于任务优先级和请求方的紧急度（如负载情况）进行排序和贪心分配。
        
    - **动态负载均衡**: 可使用滑动窗口预测未来负载，在预测到过载时主动发起任务迁移。
        

#### **2.6 分层奖励计算机制**

奖励函数 `$r_j(s,a)$` 结合了个体性能和区域协作，以平衡局部最优和全局最优。

1. **个体奖励 (`r_j^local`)**:
    
    - **正面奖励**:
        
        - **任务完成**: 根据任务的优先级加权计算奖励。
            
        - **效率奖励**: 对高CPU利用率或成功协作给予额外激励。
            
    - **负面惩罚**:
        
        - **能量消耗**: 惩罚过高的能量使用。
            
        - **任务延迟**: 惩罚过长的平均任务处理时间。
            
2. **区域协作奖励 (`r_j^regional`)**:
    
    - **负载均衡**: 奖励所在区域内卫星负载的低方差，鼓励将任务迁移到较空闲的邻居，惩罚区域负载不均。
        
    - **协作任务**: 对成功帮助邻居处理任务的行为给予奖励。
        

#### **2.7 理论分析总结**

- **环境特性**:
    
    - **部分可观测性**: 智能体仅能获取局部信息，决策具有不确定性。
        
    - **分布式决策**: 无中央控制器，需要通过学习实现隐式协调。
        
    - **环境动态性**: 卫星拓扑、任务到达、通信质量均随时间动态变化。
        
    - **多目标优化**: 需在任务完成率、能耗、延迟和负载均衡等多个目标间取得平衡。
        
- **理论优势**:
    
    - **真实性**: 严格遵循轨道动力学、通信距离等物理约束。
        
    - **可扩展性**: 架构设计支持不同规模的卫星星座。
        
    - **并行性**: 完美适配并行多智能体训练框架。
        

---
### **最终简化版环境设计方案**

#### **1. 终止条件 (已确认)**

- **唯一结束方式**: 一个 Episode 的结束**只由达到最大时隙数（例如1441步）决定**。
    
- **实现方式**:
    
    - 环境的 `step` 函数在达到最大时隙之前，`terminations` 字典永远返回 `False`。
        
    - 在最后一个时隙，`truncations` 字典返回 `True`，表示是正常截断而非任务失败。
        
- **优势**: 逻辑清晰，实现简单，完全符合强化学习标准实践。
    

#### **2. `step()` 函数实现 (已确认并补充)**

我们将采用“瞬时迁移”和“无限接收容量”的简化假设，并在此基础上**加入超时失败机制**。

**`step(self, actions)` 内部执行流程 (更新版):**

**阶段 0: 时间推进与超时检查 (新补充)**

- 这是每个 `step` 开始的第一件事：`self.current_time += 1`。
    
- **遍历所有卫星的所有任务队列**:
    
    - 对于队列中的每一个任务，检查其是否超时：`if self.current_time > (task.creation_time + task.deadline):`
        
    - **处理失败任务**:
        
        - 将该任务从队列中**移除**。
            
        - 为持有该任务的智能体（卫星）记录一个**显著的负奖励**（惩罚）。例如 `rewards[agent_id] -= 100`。
            
        - （可选）在返回的`infos`字典中记录此次失败事件，便于分析和调试：`infos[agent_id]['timeout_tasks'] += 1`。
            
- **重要性**: 这个检查发生在所有智能体动作执行之前，确保了任务的“生死”状态在当前决策时刻是确定的。
    

**阶段 1: 动作解析与执行**

- **解析动作**: 将 `actions` 字典中的卸载决策和资源分配指令解析出来。
    
- **执行任务迁移 (瞬时完成)**:
    
    - 根据卸载决策，将所有要迁移的任务从源卫星的队列中取出，放入一个临时的“中转列表”里。
        
    - **无冲突**: 由于假设接收容量无限，所有迁移请求都会成功，无需冲突解决。
        
- **执行本地处理**:
    
    - 对于所有留在本地的任务，根据资源分配向量，更新它们的处理进度。
        
    - 如果任务在本`step`内完成，将其从队列移除，并为对应智能体记录一个**正奖励**。
        

**阶段 2: 状态同步**

- **完成迁移**: 将“中转列表”里的所有任务，放入它们各自目标卫星的任务队列中。这些任务将在下一个时间步 (`t+1`) 开始参与处理或被再次决策。
    
- **生成新任务**: 调用 `_generate_stochastic_tasks()` 为当前`step`生成新的随机任务，并分配给相应的卫星。
    
- **更新其他状态**: 更新能量、位置等其他物理状态。
    

**阶段 3: 生成新观测与返回**

- 基于所有状态更新完毕后的新局面，为每个智能体生成新的局部观测。
    
- 将计算好的 `observations`, `rewards`, `terminations`, `truncations`, `infos` 返回。
#### 3.1.1 分布式部分可观测马尔可夫决策过程建模
LEO卫星星座的动态任务调度问题本质上是一个多智能体在不完全信息条件下的协同决策问题。我们将其严格建模为分布式部分可观测马尔可夫决策过程（Dec-POMDP），定义为六元组：

$$\mathcal{M} = \langle \mathcal{S}, \{\mathcal{A}_j\}_{j \in \mathcal{L}}, \mathcal{P}, \{\mathcal{R}_j\}_{j \in \mathcal{L}}, \{\Omega_j\}_{j \in \mathcal{L}}, \mathcal{O}, \gamma \rangle$$

其中$\mathcal{L}$表示LEO卫星智能体集合。

### 状态空间 (State Space) $\mathcal{S}$

状态空间$\mathcal{S}$定义了系统的全局状态，包含所有LEO卫星的物理状态（轨道位置$\mathbf{p}_j(t)$、剩余能量$e_j(t)$）、计算状态（CPU利用率$\rho_j^{cpu}(t)$、任务队列$\mathcal{Q}_j(t)$）以及星间链路质量矩阵$\mathbf{L}(t)$：

$$s(t) = \{\{\mathbf{p}_j(t), e_j(t), \rho_j^{cpu}(t), \mathcal{Q}_j(t)\}_{j \in \mathcal{L}}, \mathbf{L}(t)\}$$

### 动作空间 (Action Space) $\{\mathcal{A}_j\}_{j \in \mathcal{L}}$
为精确描述卫星智能体的决策过程，我们构建了一个**结构化的复合动作空间（Structured Composite Action Space）**。该空间将智能体在单个决策时刻的完整动作$a_j(t)$分解为两个核心部分：
#### 1. 卸载策略向量 (Offloading Policy Vector) $\mathbf{D}_j$

这是动作的第一部分，是一个离散的决策序列，其维度等于当前任务队列的长度$|\mathcal{Q}_j(t)|$。该向量为队列中的**每一个任务**指定执行位置：
$$\mathbf{D}_j(t) = (d_j^1, d_j^2, \ldots, d_j^{|\mathcal{Q}_j|})$$
其中，每个元素$d_j^k$的取值来自所有可能的任务分配目标集合$\mathcal{M}_j(t)$（例如，本地处理、邻居卫星、地面站等）。

#### 2. 本地资源分配向量 (Local Resource Allocation Vector) $\mathbf{P}_j$

这是动作的第二部分，是一个连续的决策序列。其维度是**动态变化的**，等于在第一部分卸载策略向量$\mathbf{D}_j(t)$中被指定为"本地处理"的任务数量：

$$\mathbf{P}_j(t) = (\rho_j^k | \forall k \text{ s.t. } d_j^k = \text{"local processing"})$$

其中，每个元素$\rho_j^k \in [0,1]$代表分配给本地任务$k$的计算资源比例，且所有本地任务的资源分配总和不能超过卫星的计算能力上限，即$\sum_k \rho_j^k \leq 1$。
因此，智能体$j$在时刻$t$的完整动作$a_j(t)$由这两个向量共同构成：
$$a_j(t) = \langle \mathbf{D}_j(t), \mathbf{P}_j(t) \rangle$$
这种动作空间的结构清晰地反映了决策的内在逻辑：智能体首先进行一个全局的、离散的"任务路由"决策，然后基于该决策的结果，再进行一个局部的、连续的"资源精调"决策。这要求策略网络具备生成结构化和条件性动作的能力，从而更真实地模拟复杂场景下的资源管理行为。

### 观测空间 (Observation Space) $\{\Omega_j\}_{j \in \mathcal{L}}$

观测空间$\Omega_j$反映了LEO卫星的局部感知能力。由于通信和计算约束，智能体$j$只能获得局部观测：
$$o_j(t) = \{s_j^{local}(t), \mathcal{N}_j(t), \mathcal{T}_j^{user}(t)\}$$
其中$s_j^{local}(t)$是自身状态，$\mathcal{N}_j(t)$是可通信邻居状态，$\mathcal{T}_j^{user}(t)$是覆盖区域内的用户任务请求。

### 奖励函数 (Reward Function) $\{\mathcal{R}_j\}_{j \in \mathcal{L}}$

奖励函数$\mathcal{R}_j$设计为多目标优化，平衡任务完成率、能耗、延迟、负载均衡。我们采用分层奖励机制，包含个体奖励和全局奖励：

个体奖励 (Individual Reward)考虑到任务的优先级差异，我们定义三个优先级等级：高优先级（Priority 1）、中优先级（Priority 2）、低优先级（Priority 3）。个体奖励函数为：

$$r_j^{local}(s, a) = \sum_{p=1}^{3} \alpha_p \cdot R_j^{comp,p} - \beta \cdot E_j^{cons} - \zeta \cdot D_j^{delay}$$

其中：

- $R_j^{comp,p}$表示智能体$j$完成的优先级$p$任务数量
- $\alpha_p$表示优先级$p$任务的完成奖励权重，满足$\alpha_1 > \alpha_2 > \alpha_3$
- $E_j^{cons}$表示能耗
- $D_j^{delay}$表示平均延迟

具体地，优先级权重设置为： $$\alpha_1 = 10.0, \quad \alpha_2 = 6.0, \quad \alpha_3 = 3.0$$

这种设计确保高优先级任务的完成能够获得更高的奖励，激励智能体优先处理重要任务。
#### 全局奖励 (Global Reward)
$$r_j^{regional}(s, a) = -\delta \cdot B_k(t)$$

其中$B_k(t)$表示区域$k$的负载方差，激励卫星做出有利于区域整体负载均衡的决策。
总奖励函数为：
$$r_j(s, a) = r_j^{local}(s, a) + r_j^{regional}(s, a)$$
其中$\alpha, \beta, \zeta, \delta$是权重参数，根据应用需求调节。