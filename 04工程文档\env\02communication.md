# 项目说明文档

## 项目概览

`communication_refactored.py` 是 SPACE2 卫星边缘计算仿真环境的通信链路管理核心模块，负责计算和管理卫星网络中的通信链路状态。该模块基于真实的射频通信物理模型，实现了自由空间路径损耗、信噪比和 Shannon 容量的精确计算，支持 5 种不同类型的通信链路：星间激光链路（ISL）、用户-卫星上下行链路、云-卫星上下行链路。

模块的核心价值在于将复杂的通信物理层计算抽象为统一的 API 接口，为上层的任务调度、资源分配等模块提供实时的链路质量信息。通过集成轨道动力学模块获取的距离和可见性数据，结合配置化的通信参数，动态计算每个时隙的通信矩阵，支持仿真环境中的智能决策算法研究。

## 架构总结

### 模块划分
- **CommunicationManager 类**: 核心通信管理器，统一管理所有类型的通信链路计算
- **物理层计算模块**: 实现路径损耗、接收功率、信噪比、数据速率的物理模型计算
- **链路类型处理模块**: 分别处理 ISL、卫星-地面、卫星-云三种链路类型的特定逻辑
- **缓存管理模块**: 提供通信矩阵的缓存机制，优化重复计算性能

### 模块协作方式
CommunicationManager 作为统一入口，依赖 OrbitalUpdater 获取卫星位置和可见性信息，结合配置文件中的通信参数进行物理层计算。数据流向为：轨道数据 → 距离矩阵 → 路径损耗计算 → 信噪比计算 → 数据速率计算 → 通信矩阵输出。各链路类型共享底层物理计算函数，但使用不同的功率和带宽参数。

## 核心流程

1. **初始化阶段**: 加载通信配置参数，初始化轨道更新器，建立缓存机制
2. **链路质量计算**: 基于 FSPL 公式计算自由空间路径损耗，结合天线增益和系统损耗计算接收功率
3. **信噪比计算**: 根据接收功率和系统噪声计算信噪比，考虑信道带宽的影响
4. **数据速率计算**: 使用 Shannon 公式计算理论容量，结合编码效率得到实际数据速率
5. **矩阵生成**: 为每种链路类型生成包含数据速率、信噪比、距离、可见性的完整通信矩阵

关键的业务逻辑是基于链路预算方程：Pr = Pt + Gt + Gr - FSPL - L_other，以及 Shannon 容量公式：C = B × log2(1 + SNR)。

## 外部集成

- **与 OrbitalUpdater 的集成**: 获取卫星位置、距离矩阵和可见性矩阵，作为通信计算的空间基础
- **与 TimeManager 的集成**: 通过轨道更新器间接集成，确保时间上下文的一致性
- **与任务管理模块的集成**: 提供链路质量指标，支持任务路由和资源分配决策
- **与配置系统的集成**: 从 YAML 配置文件加载通信参数，支持参数化仿真实验

## 关键设计决策

- **物理模型选择**: 采用自由空间路径损耗模型而非复杂的信道模型，平衡计算精度和仿真效率
- **链路类型分离**: 将 ISL 激光链路和 RF 链路分别处理，激光链路使用固定高速率，RF 链路基于物理计算
- **缓存策略设计**: 实现按时隙的通信矩阵缓存，避免重复的复杂物理计算
- **单位统一管理**: 提供 W/dBm 功率转换函数，确保计算过程中单位的一致性
- **向量化计算**: 使用 NumPy 数组操作处理矩阵计算，提升大规模网络的计算性能

## 技术栈

- **主要框架**: numpy (数值计算), yaml (配置管理)
- **数据库**: 无独立数据库，依赖配置文件和轨道数据
- **其他重要库**: math (数学函数), logging (日志记录), pathlib (路径处理)

## 重要配置

- **rf_carrier_freq_hz**: 射频载波频率 (14GHz)，影响路径损耗计算
- **antenna_gain_db**: 天线增益 (33dB)，决定链路预算中的增益项
- **system_noise_dbm_hz**: 系统噪声功率密度 (-174dBm/Hz)，影响信噪比计算
- **isl_tra_rate**: 星间激光链路速率 (50Gbps)，ISL 的固定数据速率
- **各链路功率参数**: p_u_w (5W), p_su_w (10W), p_sc_w (15W), p_c_w (50W)，决定不同链路的发射功率
- **coding_efficiency**: 编码效率 (0.7)，将理论容量转换为实际可用速率

## 学习要点

- **通信物理层建模**: 掌握了射频通信的基本物理原理，包括路径损耗、链路预算、Shannon 容量等核心概念
- **矩阵化计算优化**: 学会使用 NumPy 广播机制处理大规模矩阵运算，避免嵌套循环的性能问题
- **模块化设计实践**: 实现了物理计算函数的复用，不同链路类型共享底层计算逻辑但使用不同参数
- **缓存机制设计**: 在通信计算这种计算密集型场景中，合理的缓存策略能显著提升仿真性能
- **配置驱动开发**: 通过外部配置文件管理通信参数，支持不同仿真场景的快速切换和参数调优
