
## 项目概览

`orbital.py` 是 SPACE2 卫星边缘计算仿真环境的轨道动力学核心模块，负责管理 72 颗 LEO 卫星在 1441 个时隙内的轨道运动和空间关系计算。该模块基于真实的卫星轨道数据，提供精确的卫星位置信息、可见性判断和距离计算功能，为上层的通信链路管理、任务分配等模块提供基础的物理层支撑。

模块的核心价值在于将复杂的三维空间轨道计算抽象为简洁的 API 接口，支持卫星间、卫星-地面站、卫星-云中心三种类型的可见性矩阵计算，并通过缓存机制和向量化计算优化性能，确保仿真环境能够高效处理大规模的空间网络拓扑变化。

## 架构总结

### 模块划分
- **Satellite 类**: 封装单颗卫星的基本信息（位置、光照状态、时间上下文）
- **GroundStation 类**: 管理地面用户终端和云计算中心的位置信息
- **OrbitalUpdater 类**: 核心轨道更新器，负责数据加载、位置计算和可见性矩阵生成

### 模块协作方式
OrbitalUpdater 作为中心协调器，从 CSV 数据文件加载卫星轨道数据，结合 TimeManager 进行时间管理，为每个时隙生成 Satellite 对象集合。通过 ECEF 坐标系统一计算三维距离，生成三种类型的可见性矩阵供通信模块使用。数据流向为：CSV 数据 → 内存缓存 → 时隙查询 → 可见性计算 → 矩阵输出。

## 核心流程

1. **初始化阶段**: 加载配置文件和卫星轨道数据，建立时间戳索引，初始化地面站和云中心位置
2. **时隙查询**: 根据指定时隙从缓存的轨道数据中提取卫星位置，创建 Satellite 对象
3. **可见性计算**: 使用向量化的 ECEF 坐标转换和距离计算，生成布尔型可见性矩阵和浮点型距离矩阵
4. **结果缓存**: 将计算结果存储在内存缓存中，避免重复计算提升性能

关键的业务逻辑是基于地球中心地固坐标系（ECEF）的三维距离计算，通过设定的可见性阈值判断空间实体间的通信可达性。

## 外部集成

- **与 TimeManager 的集成**: 接收时间管理器实例，获取时隙到物理时间的映射关系，确保时间上下文的一致性
- **与 CommunicationManager 的集成**: 提供距离矩阵和可见性矩阵，作为通信链路质量计算的基础输入
- **与 TaskTracking 的集成**: 支持任务接入时的最优卫星选择，基于地面站到卫星的距离进行智能路由

## 关键设计决策

- **ECEF 坐标系选择**: 采用地心地固坐标系而非球面坐标，确保三维距离计算的精度，避免球面几何的复杂性
- **向量化计算优化**: 使用 NumPy 广播机制批量计算距离矩阵，相比循环计算提升数十倍性能
- **多级缓存策略**: 实现卫星位置缓存和可见性矩阵缓存，平衡内存使用和计算效率
- **统一时间管理**: 集成 TimeManager 而非独立时间处理，确保全系统时间同步

## 技术栈

- **主要框架**: pandas (数据处理), numpy (数值计算)
- **数据库**: CSV 文件存储轨道数据
- **其他重要库**: yaml (配置管理), pathlib (路径处理), logging (日志记录)

## 重要配置

- **visibility_threshold_m**: 卫星间可见性阈值距离 (5500000m)，影响星间链路的连通性
- **visibility_earth_m**: 地面站可见性阈值 (2500000m)，决定卫星对地面的覆盖范围
- **cloud_visibility_threshold_m**: 云中心可见性阈值 (3300000m)，影响卫星到云的连接能力
- **leo_altitude_m**: LEO 卫星轨道高度 (1200000m)，用于 ECEF 坐标计算

## 学习要点

- **空间几何计算**: 掌握了 ECEF 坐标系的应用，理解三维空间中距离计算的数学原理
- **性能优化实践**: 学会使用 NumPy 向量化操作替代 Python 循环，显著提升大规模矩阵计算效率
- **缓存设计模式**: 实现了多层次缓存机制，在内存和计算时间之间找到平衡点
- **模块解耦设计**: 通过依赖注入的方式集成 TimeManager，避免硬编码依赖，提升代码的可测试性和可维护性
