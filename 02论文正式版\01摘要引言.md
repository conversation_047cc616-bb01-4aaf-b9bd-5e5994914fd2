轨道上的具身智能：通过动态域策略演进实现卫星星座的集体智慧

## 摘要
1
下一代天地一体化网络要求低地球轨道（LEO）卫星星座从传统通信中继节点演进为具备复杂计算能力的自主边缘节点。然而，LEO星座固有的高动态拓扑变化、严格的星载资源约束以及大规模并发任务需求，对实现高效分布式在轨决策构成了根本性技术挑战。传统地面集中式调度范式和现有多智能体强化学习方法均无法有效应对此类复杂性。

本文提出轨道感知自适应学习框架（OAAL），将LEO卫星重构为具身智能体，其中物理平台构成"身体"，决策算法构成"心智"，通过感知-行动-学习闭环实现复杂目标。OAAL的核心创新在于双向学习范式：（1）引入地理绑定的"策略域"概念，作为融合历史访问卫星成功经验的动态知识库，代表区域性集体智慧；（2）独立卫星智能体通过知识蒸馏机制在穿越不同策略域时快速适应局部最优策略。

为解决单步决策与批量任务处理的根本矛盾，本文设计了基于Transformer的生成式动作空间，使智能体能够在单次决策中输出完整任务队列的调度方案。同时，通过图注意力网络实现智能体间的自适应通信协调。高保真仿真验证表明，OAAL在任务完成率、端到端延迟和系统能效等关键指标上显著优于现有基准算法，为构建完全自主的分布式空间计算系统提供了理论基础和技术路径。

2
下一代天地一体化网络要求低地球轨道（LEO）卫星星座从通信中继演进为自主边缘计算节点，但这面临着高动态拓扑、严苛星上资源与大规模并发任务三大挑战。传统地面调度与现有 MARL 方法因其集中式瓶颈或局部观测限制，难以应对这一复杂困境。

本文提出一种轨道感知的自适应学习框架（OAAL）。该框架的核心是一种创新的双向学习范式：首先，引入地理绑定的“策略域”作为动态知识库，通过性能加权的策略融合算法，将历史上优秀卫星的经验提炼为区域性集体智慧；其次，LEO卫星智能体在穿越不同策略域时，通过知识蒸馏快速继承域策略，实现对局部环境的快速适应。这种“上行经验聚合”与“下行知识传递”的闭环机制，有效解决了分布式学习中的非平稳性和低效性问题。

为解决传统单步决策与批量任务处理的根本矛盾，我们设计了基于Transformer的生成式调度器，使智能体能单次前向传播生成整个任务队列的最优分配序列。此外，通过图注意力网络（GAT）实现邻居间的高效动态协同。高保真仿真验证，OAAL在任务完成率、平均延迟和系统能效等关键指标上均显著优于前沿基线算法，为构建高韧性、全自主的分布式空间计算系统提供了坚实的理论与技术蓝图。

3
低地球轨道（LEO）星座正经历一场从通信中继到分布式计算平台的深刻变革，这催生了一类全新的、以卫星为载体的自主**具身智能体**（Embodied Agents）。然而，这些智能体的决策“心智”（算法）受制于其物理“身体”（卫星平台）的严苛约束——包括高动态的轨道力学、受限的星上能量与计算资源。这使得传统地面集中式或简单的分布式方法难以胜任，对设计新一代的在轨自主决策范式提出了迫切需求。

为应对此挑战，本文提出一种**轨道感知的自适应学习框架（OAAL）**。OAAL的核心是一种创新的**集体智慧演进机制**，它通过地理绑定的“策略域”将时空经验转化为可迁移的知识。该机制包含一个闭环的**双向知识流**：1）**下行知识蒸馏**，使得智能体在进入新区域时能快速继承集体智慧，实现高效适应；2）**上行经验聚合**，通过性能加权的策略融合，将智能体的成功经验提炼并反哺给策略域。

在智能体层面，我们设计了基于Transformer的**生成式序列调度器**，它使智能体能单次前向传播生成整个任务队列的完整调度方案，实现了从**原子化动作**到**批量化任务序列**生成的范式跨越。同时，我们采用图注意力网络（GAT）以实现动态拓扑下的星间自适应协同。高保真仿真验证，OAAL在任务完成率、端到端延迟和系统能效等关键指标上均显著优于前沿基准算法。本研究为未来分布式具身智能系统，特别是那些运行在极端、高动态环境下的系统，提供了坚实的设计蓝图与实现范式。
## 引言
全球数字化转型的加速推进正在重塑人类对连接性和计算能力的根本需求。传统地面网络基础设施在覆盖偏远地区、海洋和极地等区域时面临显著局限，而新兴应用如实时地球观测、全球物联网和深空探测等对低延迟、高可靠性计算服务的需求日益迫切^[1-3]^。在此背景下，以低地球轨道（LEO）卫星星座为核心的天地一体化网络（Space-Air-Ground Integrated Network, SAGIN）已成为实现全球无缝连接的关键技术路径^[4,5]^。

然而，LEO卫星星座的独特物理特性——包括高速轨道运动（约7.8 km/s）、有限的星载资源以及动态变化的网络拓扑——对传统的资源调度和任务分配范式构成了根本性挑战^[6,7]^。现有的集中式优化算法和多智能体强化学习方法在面对这种高动态、资源受限的分布式环境时表现出明显的不适应性^[8,9]^。更为关键的是，传统方法将卫星视为被动的计算节点，忽略了其作为具备感知、决策和行动能力的自主实体的潜力。

近年来，具身智能（Embodied Intelligence）的概念为解决复杂动态环境中的自主决策问题提供了新的理论框架^[10,11]^。具身智能强调智能体通过其物理"身体"与环境进行持续交互，在感知-行动-学习的闭环过程中发展出适应性行为^[12]^。这一范式与LEO卫星的本质特征高度契合：卫星作为物理实体在三维空间中运动，通过传感器感知环境状态，通过星载计算资源处理任务，并通过通信系统与其他节点协调。将LEO卫星重新概念化为具身智能体，为构建真正自主的分布式空间计算系统开辟了新的可能性。

当前研究面临的核心挑战可归纳为四个方面。首先，传统的集中式调度方法依赖全局状态信息和中央决策节点，在大规模分布式卫星网络中引入了不可接受的通信延迟和能耗开销^[13,14]^。其次，现有学习算法未能充分利用任务需求的地理空间特性和时空相关性，导致学习效率低下和策略泛化能力不足^[15,16]^。第三，孤立的单星决策忽略了星间协同的潜力，容易导致资源利用不均和系统性能次优^[17,18]^。最后，现有方法缺乏对故障场景的考虑，在面对卫星失效或通信中断时缺乏有效的恢复机制^[19,20]^。

为应对这些挑战，本文提出了轨道感知自适应学习框架（Orbit-Aware Adaptive Learning Framework, OAAL），这是一个基于具身智能理念的分布式学习系统。OAAL的核心创新在于将每颗LEO卫星重构为具身智能体，其中卫星平台构成物理"身体"，决策算法构成认知"心智"。该框架引入了三个关键机制：（1）地理绑定的策略域（Strategy Domain）概念，作为区域性集体智慧的载体；（2）基于知识蒸馏的自适应学习机制，使智能体能够快速适应不同地理区域的任务特性；（3）基于Transformer的生成式动作空间设计，解决了单步决策与批量任务处理之间的根本矛盾。

本文的主要贡献包括：（1）首次将具身智能概念引入卫星网络资源调度领域，提出了一种全新的分布式学习范式；（2）设计了创新的双向学习机制，实现了个体智能体学习与集体智慧演进的有机结合；（3）解决了多智能体强化学习中单步决策与批量任务处理的技术难题；（4）构建了大规模高保真仿真平台，验证了所提方法在复杂动态环境中的有效性。实验结果表明，OAAL在任务完成率、端到端延迟和系统能效等关键指标上显著优于现有基准算法，为构建下一代自主空间计算系统提供了重要的理论基础和技术路径。