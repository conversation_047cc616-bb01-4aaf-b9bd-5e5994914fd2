**Title:**  
Orbit-Aware Adaptive Learning: Embodied Intelligence for Autonomous Distributed Computing in Low Earth Orbit

**Abstract:**  
Low Earth Orbit (LEO) satellite constellations are undergoing a transformative shift from mere communication relays to distributed computational platforms, ushering in a new class of autonomous, satellite-based embodied agents. However, the decision-making algorithms of these agents are fundamentally constrained by their physical embodiment, characterized by stringent limitations such as highly dynamic orbital mechanics, restricted on-board energy, and computational resources. These embodiment constraints render conventional terrestrial centralized or simplistic distributed approaches inadequate, highlighting an urgent need for innovative in-orbit autonomous decision-making paradigms.

To address this challenge, we propose the Orbit-Aware Adaptive Learning (OAAL) framework, an embodied intelligence-based distributed learning system designed explicitly for LEO satellite constellations. At its core, OAAL introduces a novel collective intelligence evolution mechanism leveraging geographically-bound strategy domains that transform spatiotemporal experiences into transferable knowledge. The framework employs a closed-loop, bi-directional knowledge flow: downward knowledge distillation enables agents to swiftly inherit collective intelligence upon entering new regions, facilitating rapid adaptation; conversely, upward experience aggregation synthesizes and propagates the optimal strategies from leading agents back into the strategy domain.

At the agent level, we introduce a Transformer-based generative sequence scheduler, enabling agents to generate comprehensive task schedules via a single forward pass, effectively bridging the gap between atomic action selection and batch task sequence generation. Additionally, we utilize Graph Attention Networks (GAT) to dynamically coordinate inter-satellite collaboration under continuously evolving topologies. Extensive high-fidelity simulations demonstrate that OAAL significantly outperforms state-of-the-art benchmarks across key performance metrics, including task completion rates, end-to-end latency, and system energy efficiency. Our study provides a robust blueprint and implementation paradigm for future distributed embodied intelligence systems, particularly for those operating in extreme, highly dynamic environments.