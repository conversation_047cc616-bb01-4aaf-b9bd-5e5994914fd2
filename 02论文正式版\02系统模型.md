# 摘要引言素材

[具身智能](https://www.notion.so/23276c006de7802ca3adf6857965c8d7?pvs=21)

# 0. 摘要

为了应对下一代天地一体化网络中复杂的资源调度挑战，我们提出了一个**轨道感知自自适应学习框架（Orbit-Aware Adaptive Learning Framework, OAAL）**。该框架专为大规模低地球轨道（LEO）卫星星座的边缘计算环境而设计，旨在克服高动态网络拓扑、异构星上资源、受限的星间链路以及多样化的任务需求所带来的固有挑战。OAAL框架创新性地融合了**分布式多智能体决策、地球同步轨道（GEO）卫星辅助的全局经验指导、基于图神经网络（GNN）的时空流量预测与主动缓存，以及鲁棒的故障恢复机制**。通过在一个高保真、大规模的仿真环境中进行验证，我们的结果表明，OAAL框架在任务完成率、端到端时延和系统能效方面，均显著超越了现有的基准算法，为未来空间任务的在轨实时智能处理提供了全新的解决方案。

# 1. 引言

随着全球连接需求的激增，以低轨（LEO）卫星星座为核心的天地一体化网络（SAGIN）已成为实现无缝、广域覆盖和低时延通信的关键基础设施。将计算任务从地面中心迁移至卫星边缘，能够有效赋能实时地球观测、全球物联网以及深空探测等多种前沿应用。然而，LEO卫星星座的高度**动态性和资源异构性**，对传统的地面资源调度范式提出了前所未有的挑战。

过往的不足：

1. 首先，传统的集中式优化算法或强化学习方法，通常依赖于一个能够获取**全局状态信息**的中心决策节点。在庞大的卫星网络中，这种假设是不切实际的，因为它会引入通信延迟和能量消耗，与边缘计算的低时延初衷背道而驰。因此，我们主张将每颗卫星设计为**独立的、具备自主决策能力的智能体**，以适应动态变化的环境和任务请求。
    
2. 现有方法未能充分利用任务需求的**地理空间特性**。任务请求在全球范围内的分布并非均匀随机，而是呈现出显著的区域性时空模式。以往的学习策略仅依赖于单颗卫星与其直接环境的被动交互，学习效率低下。为了解决这一问题，我们创新性地引入了**GEO-LEO分层学习架构**。我们将全球划分为42个独立的地理区域，并利用高轨GEO卫星作为全局经验学习与分发中心。GEO卫星汇聚并学习各区域的任务特性与LEO卫星的最佳处理策略，随后持续地向即将进入这些区域的LEO卫星**主动“播报”先验知识（Prioritized Experience Replay）**。这种前瞻性的指导极大地加速了LEO卫星的策略收敛速度，并显著降低了其在陌生环境中的探索成本。
    
3. 孤立的单星决策忽略了卫星间协同带来的全局最优潜力，容易导致资源利用率低、任务拥塞和响应时延增加等问题。针对这一不足，我们设计了一种高效的星间协作机制，使每颗卫星能够在限定的通信范围内与其邻近卫星进行信息共享与任务协同决策。具体而言，卫星周期性广播自身的任务负载、计算资源状态和链路可达性信息，并根据邻居反馈动态调整任务卸载策略。该机制支持基于局部网络图的协同调度策略，在保证通信开销可控的前提下，实现区域层面的计算负载均衡、链路资源优化与任务时延最小化。
    
4. 过往的研究仅考虑了全部卫星正常工作的情况，但实际上星座在运行过程中可能受到电磁干扰而导致个别卫星失效。针对这一问题，我们定义了两种故障情况：运行过程中的随机卫星故障，以及某一时刻特定区域的大规模卫星故障。为应对这两种情况，我们结合故障恢复机制和GEO卫星的全局优化机制，实现了整体性能的优化。
    
5. 为了确保研究的有效性和可扩展性，我们构建了一个**大规模、高保真的仿真平台**。与以往工作中卫星与用户数量较少的简化模型不同，我们的平台模拟了拥有数十颗卫星和全球用户的复杂动态环境。任务生成模型也摒弃了简单的随机分布，而是采用了更贴近现实世界的、具有**地理依赖性和时空关联性**的生成算法，从而为OAAL框架的性能验证提供了坚实的基础。
    

# 2. 系统模型

[任务描述](https://www.notion.so/20f76c006de780c9acf3dd304567dd8d?pvs=21)

|Notation|中文|参数设置|
|---|---|---|
|系统相关参数|||
|$UEs$|用户终端集合|420个地面用户|
|$LEOs$|低地球轨道卫星集合|72|
|area_num|策略域数量|42|
|通信相关参数|||
|$\sigma^2$|射频信道背景噪声|-100 dBm|
|$f_{node}$|低地球轨道卫星j的计算能力|10 TOPS|
|$B_{us}$|用户终端与卫星间的上行带宽|100 MHz|
|$B_{sc}$|卫星与地面云中心的下行带宽|150 MHz|
|$P_{u}$|用户终端的发射功率|5 W|
|$P_{sc}$|卫星对云中心的发射功率|15 W|
|任务相关参数|||
|$Tasks(t)$|时隙$t$时的任务集合||
|$S_k(t)$|时隙$t$时任务$k$的数据大小|三类任务5-50 MB|
|$\rho_k$|任务k每比特的cpu周期数|1-4 GOPS/MB|
|$\tau_k$|任务k的可接受最大延迟|5-35 s|
|能耗相关参数|||
|$P_L$|低轨卫星LEO的充电功率|15 kW|
|$\tau$|每个CPU周期的计算能力|4J/Gcycles|
|$p_{L_{j,j(n)}}$|低地球轨道卫星$j$与低地球轨道卫星$j(n)$之间的传输功率|15 W|
|能耗相关计算|||
|$E_{j_C}$|低地球轨道卫星$j$的计算能耗||
|$E_{C_T}$|低轨卫星与云中心之间的传输能耗||
|$E_{j(n)_T}$|低地球轨道卫星之间的星间链路的传输能耗||
|$E_{U_T}$|LEO卫星到用户的传输能耗||
|延迟相关计算|||

本章旨在构建一个全面的卫星边缘计算（Satellite Edge Computing, SEC）系统模型，为后续的动态任务调度与资源分配算法设计提供坚实的理论基础。该模型将从系统架构、网络通信、任务特性、计算与能耗等多个维度进行详细阐述，精确刻画在卫星网络环境下进行边缘计算所面临的独特挑战与约束。

## 2.1 系统架构

为应对地面用户日益增长的实时计算需求与地面网络覆盖的局限性，我们设计了一个集成的天-地协同计算架构。如图所示，该架构是一个由四个关键层面构成的异构系统，各层面协同工作，以实现高效、广域覆盖的智能服务。

1. **地面用户层 (Ground User Layer)**：此层面由地理上广泛分布的终端设备组成，如物联网（IoT）传感器、智能车辆或个人移动设备。这些用户是计算任务的发起者，负责数据采集，并将需要处理的任务请求发送至卫星网络。我们定义地面用户集合为 U={1,2,...,U}。
2. **LEO卫星边缘层 (LEO Edge Layer)**：此层面是整个系统的核心，由一个近地轨道（Low Earth Orbit, LEO）卫星星座构成。这些LEO卫星不仅作为通信中继，更被赋予了计算和存储能力，充当边缘服务器的角色。它们负责接收地面用户的任务请求，并根据任务需求和自身状态进行本地计算、或将任务转发至其他LEO卫星及云中心。由于体积、重量和功率的严格限制，单个LEO卫星的计算与储能资源是有限的。在本研究中，我们构建一个包含 M 个轨道，每个轨道分布 N 颗卫星的Walker Delta星座模型，卫星总数为 S=M×N。为便于分析与仿真，本文采用一个包含 6×6=36 颗卫星的简化星座。LEO卫星的集合表示为 S={1,2,...,S}。星座内通过星间激光链路（Inter-Satellite Links, ISLs）构建了一个动态的网状网络拓扑，保证了低延迟的星间数据交换。
3. **GEO卫星核心控制层 (GEO Control Layer)**：此层面由多颗对地静止轨道（Geostationary Earth Orbit, GEO）卫星组成。由于其广阔的覆盖范围和与地面相对固定的位置，GEO卫星非常适合扮演“全局协调者”的角色。它负责从LEO卫星网络收集宏观的状态信息（如负载分布、链路质量），运行复杂的全局优化算法，并将形成的全局策略或控制信令分发给下层的LEO卫星，指导其进行更优化的本地决策。
4. **地面云计算层 (Cloud Computing Layer)**：此层面由功能强大的地面数据中心组成，作为中心化的、近乎无限的计算和存储资源池。对于那些计算极其密集或需要海量历史数据、而任何单个LEO卫星都无法处理的复杂任务，可以经由LEO卫星网络回传至云中心进行处理。

这四层架构通过协同工作，将云计算的能力延伸至网络边缘，形成了云、边、端一体化的服务范式，为全球范围内的用户提供低延迟、高可靠的计算服务。

## 2.2 任务模型

### **2.2.1 任务生成模型**

真实世界中的计算任务请求往往并非独立同分布，而是呈现出时间和空间上的突发性和关联性。例如，某一区域的突发事件可能导致大量相关联的监测和分析任务同时产生。为了精确捕捉此特性，我们采用**单变量霍克斯过程 (Univariate Hawkes Process)** 来对任务的到达进行建模。霍克斯过程是一种自激励点过程，其在时刻 $t$ 的条件强度函数 $\lambda(t)$ 定义为： $\lambda(t) = \mu + \int_{-\infty}^{t} g(t-s) dN(s) = \mu + \alpha \sum_{t_i < t} e^{-\beta(t-t_i)}$

其中：

- $t_i$ 是历史中第 $i$ 个任务的到达时间。
- $\mu > 0$ 是背景强度（base intensity），代表任务的自发产生率。
- $g(t) = \alpha e^{-\beta t}$ 是激励核函数（triggering kernel），其中 $\alpha > 0$ 表示一次历史事件对未来事件发生率的影响强度，$\beta > 0$ 表示该影响随时间推移的衰减速率。 此模型表明，每个任务的到来都会暂时性地增加后续任务到来的概率，从而能够有效地模拟任务的簇状爆发。

### **2.2.2 任务属性**

每个在时隙 $t$ 到达的任务 $T_k$ 都被定义为一个多元组，包含了其处理所需的全部信息： $\mathcal{T}_k = \{A_k, S_k, C_k, D_k^{\text{max}}, P_k\}$

各属性具体说明如下：

- **到达时间 ($A_k$)**：任务 $T_k$ 由地面用户生成并发出请求的时刻。
- **数据大小 ($S_k$)**：执行任务所需的输入数据量，单位为比特（bits）。此数据量决定了任务上传至卫星所需的通信开销。
- **计算工作量 ($C_k$)**：完成任务所需的总计算量，单位为CPU周期数（CPU cycles）。该值决定了在特定计算节点上的处理延迟。
- **最大截止时间 ($D_k^{\text{max}}$)**：任务必须完成并返回结果的绝对时间点。任何超过此时间点的完成都将被视为任务失败。
- **静态优先级 ($P_k$)**：根据任务的业务类型或重要性预先设定的一个数值。$P_k$ 越高，代表任务越重要。

### **2.2.3 任务生命周期**

任务从生成到终结遵循一个明确的生命周期。地面用户在任务生成后，会选择一个可见的LEO卫星尝试上传。若任务在截止时间 $D_k^{\text{max}}$ 内未收到处理结果，用户会重新提交该任务。为避免网络资源滥用，我们设定一个最大重试次数（例如，3次），若连续重试后任务依然失败，则该任务被最终标记为失败，不再发送。

## **2.3 通信模型 (Communication Model)**

数据在系统各层之间的有效流动是实现任务卸载与协同计算的前提。本节对系统中关键通信链路的性能进行数学建模，包括数据传输速率、延迟和能耗。

### **2.3.1 链路类型与信道模型**

系统主要包含以下三种通信链路：

1. **地-星上行链路 (Ground-to-LEO Uplink)**：此链路采用射频（Radio Frequency, RF）通信。其信道质量受到路径损耗、阴影和多径衰落的综合影响。信道增益 $|h_{u,s}|^2$ 由大尺度路径损耗 $L_{\text{pl}}$ 和小尺度莱斯衰落 $L_{\text{fade}}$ 共同决定： $|h_{u,s}|^2 = L_{\text{pl}} \cdot L_{\text{fade}}$
    - **路径损耗**：采用Friis自由空间路径损耗公式计算：$L_{\text{pl}} = \left(\frac{c}{4\pi d_{us} f_{\text{rf}}}\right)^2$，其中 $c$ 是光速，$d_{us}$ 是用户 $u$ 与卫星 $s$ 间的瞬时距离，$f_{\text{rf}}$ 是射频载波频率。
    - **小尺度衰落**：考虑到LEO通信中通常存在较强的直射（Line-of-Sight, LoS）分量，我们采用**莱斯衰落 (Rician Fading)** 模型。其特性由莱斯$K$因子定义，K值代表LoS分量与非直射散射分量的功率比。
2. **星间链路 (Inter-Satellite Link, ISL)**：为实现高带宽、低延迟的星间通信，我们假设LEO卫星间采用**激光通信技术 (Laser Communication Technology, LCT)**。激光链路几乎不受频谱干扰，但对精准指向、捕获和跟踪（Pointing, Acquisition, and Tracking, PAT）有极高要求。
3. **星-云链路 (LEO-to-Cloud Link)**：此链路作为回传链路，可采用高频段的射频通信或自由空间光通信，其建模方式与地-星链路或星间链路类似，但通常具有更稳定的信道条件和更高的带宽。

### **2.3.2 数据传输速率模型**

基于上述信道模型，各链路的数据传输速率可计算如下：

- **地-星上行链路速率 ($R_{us}$)**：根据香农-哈特利定理，并考虑多用户干扰，用户 $u$ 到卫星 $s$ 的上行链路速率为： $R_{us} = B_{us} \log_2 \left(1 + \frac{P_u |h_{u,s}|^2}{\sum_{j \neq u, j \in \mathcal{U}_s} P_j |h_{j,s}|^2 + \sigma^2}\right)$ 其中，$B_{us}$ 是上行链路带宽，$P_u$ 是用户终端的发射功率，$\mathcal{U}_s$ 是当前由卫星 $s$ 服务的用户集合，分母中的求和项代表来自其他用户的同频干扰，$\sigma^2$ 是高斯白噪声功率。


- 星间链路直接采用50Gbps的传输速率
- **星-云链路速率 ($R_{sc}$)**：假设为无干扰的专用链路，其速率可简化为： $R_{sc} = B_{sc} \log_2 \left(1 + \frac{P_s |h_{s,c}|^2}{\sigma^2}\right)$ 其中，$P_s$ 是卫星的发射功率，$|h_{s,c}|^2$ 是卫星到云地面站的信道增益。

### **2.3.3 通信延迟与能耗**

对于一个数据大小为 $S_k$ 的任务，其在任意一段链路上（以地-星链路为例）的通信开销包括：

- **传输延迟 ($T_{\text{tx}, us}$)**：数据块从发送端完整发送出去所需的时间。 $T_{\text{tx}, us} = \frac{S_k}{R_{us}}$
- **传播延迟 ($T_{\text{prop}, us}$)**：信号在物理空间中传播所需的时间。 $T_{\text{prop}, us} = \frac{d_{us}}{c}$
- **总通信延迟 ($T_{\text{comm}, us}$)**：$T_{\text{comm}, us} = T_{\text{tx}, us} + T_{\text{prop}, us}$。
- **通信能耗 ($E_{\text{comm}, us}$)**：仅考虑发送端的能耗。 $E_{\text{comm}, us} = P_u \cdot T_{\text{tx}, us} = P_u \cdot \frac{S_k}{R_{us}}$ 其他链路（ISL, LEO-Cloud等）的延迟和能耗可同理计算，只需替换相应的速率、距离和发射功率参数。

## **2.4 计算模型 (Computation Model)**


每个任务$T_k$的计算属性由两个独立参数定义：

- **数据大小 ($S_k$)**: 执行任务所需的输入数据量，单位为MB。
- **计算密度 ($\rho_k$)**: 处理每MB数据所需的计算操作数，单位为 TO/MB。该参数反映了任务本身的计算复杂度。

因此，完成任务$T_k$所需的**总计算工作量**为：

$$C_k = S_k \cdot \rho_k \quad \text{(TO)}$$

每个计算节点$n \in \mathcal{N}$的计算能力由其**计算频率**$f_n$表征，单位为TOPS。

任务$T_k$在节点$n$上的**计算延迟**由总计算工作量和节点计算能力的比值确定：
	
$$T_{\text{comp},n}(k) = \frac{C_k}{f_n} = \frac{S_k \cdot \rho_k}{f_n} \quad \text{(seconds)}$$

$\zeta_n$是与节点$n$处理器硬件架构相关的能效系数，单位为J/T-Op。

执行任务$T_k$所消耗的**总计算能耗**为：

$$E_{\text{comp},n}(k) = \zeta_n \cdot C_k = \zeta_n \cdot (S_k \cdot \rho_k) \quad \text{}$$


## 2.5 排队模型

为应对卫星节点面临的多任务并发、资源受限与动态变化的挑战，传统的先进先出（FIFO）排队策略难以满足复杂的任务处理需求。因此，本节构建一个基于动态优先级评分的排队调度模型（Dynamic Priority Scoring based Queuing and Scheduling Model, DPSQ），以实现对不同任务的高效和差异化处理。

首先，我们对进入卫星节点等待队列的任务 Ti 的关键属性进行定义，包括其**静态优先级** P_i、**绝对截止时间** $D_i$、**数据大小** $S_i$、**计算复杂度** $C_i$ 以及任务处理失败的**丢弃惩罚** $W_i$。处理一个任务 $T_i$ 所需的预估总时延 $T_{proc,i}$ 是其通信时延与计算时延之和，即 $T_{proc,i}=\frac{S_i}{B_{link}}+\frac{C_i}{F_{sat}}$，其中 $B_{link}$ 和 $F_{sat}$ 分别代表当前可用的星地链路带宽和卫星的计算能力。

本模型的核心在于为等待队列 $Q$ 中的每个任务 $T_i$ 在当前时刻 $t_{now}$ 计算一个动态优先级分数，调度器依据此分数选择最优任务。其评分函数定义如下：

$Score(T_i,t_{now})=w_p \cdot f_p(P_i)+w_d \cdot f_d(D_i,t_{now})-w_c \cdot f_c(S_i,C_i)$

该函数由三个加权因子构成：

- **优先级因子 $f_p(P_i)=P_i$**: 直接反映任务的业务重要性。
- **紧迫性因子 $f_d(D_i,t_{now})=\frac{1}{(D_i-t_{now})+\epsilon}$**: 反映任务的时效性压力。随着任务接近其截止时间，该项分值会急剧升高，使其获得更高的调度优先级。$\epsilon$ 是一个防止分母为零的极小正常数。
- **成本因子 $f_c(S_i,C_i)=T_{proc,i}$**: 代表处理该任务所需的总时间开销。成本越高的任务，其优先级分数会相应降低，以避免长时间占用宝贵的计算和通信资源。

权重系数 $w_p,w_d,w_c$ 为可根据系统优化目标调整的超参数，用以平衡任务重要性、时效性和执行成本三者之间的关系。

调度流程如下：当卫星计算资源可用时，调度器为队列中所有未超时的任务计算动态优先级分数。它会优先选择得分最高的任务 $T_{best}$。在最终派发执行前，系统会进行一次可行性检查：若预估完成时间 $t_{now}+T_{proc,best}$ 超过其截止时间 $D_{best}$，则判定该任务已无法按时完成，它将被主动丢弃，系统承担其对应的丢弃惩罚 $W_{best}$，并立即重新在剩余任务中选择次优任务。

综上，此调度机制通过对本地决策的实时优化，旨在间接最小化由任务丢弃惩罚和处理延迟构成的系统总代价，从而在动态变化的卫星边缘计算环境中实现高效、可靠的资源调度。

## **2.6 故障模型 (Fault Model)**

卫星网络运行于复杂多变的空间环境中，面临着由硬件老化、能耗衰竭、空间碎片撞击及恶劣空间天气等多种因素引发的故障风险。为评估并提升我们系统的鲁棒性与服务韧性，本节将对卫星节点的故障与恢复过程进行数学建模。

### **2.6.1 故障状态定义**

我们为星座中的每一颗LEO卫星 $s \in \mathcal{S}$ 在每一个离散的时隙 $t$ 定义一个二元故障状态指示函数 $\phi_s(t)$： $\phi_s(t) = \begin{cases} 1, & \text{若卫星 } s \text{ 在时隙 } t \text{ 正常工作} \\ 0, & \text{若卫星 } s \text{ 在时隙 } t \text{ 处于故障状态} \end{cases}$ 当 $\phi_s(t) = 0$ 时，卫星 $s$ 被认为完全失效，无法进行任何计算任务，也无法通过其ISL端口接收或转发数据。其承载的所有计算资源和通信链路在此时隙内均不可用。

### **2.6.2 故障场景建模**

本文重点关注以下两种典型的故障场景：

1. **随机失效场景 (Random Failure Scenario)**：此场景模拟由节点内部原因（如元器件损耗）导致的独立、偶发性故障。我们假设在每个时隙 $t$，对于任意一颗处于正常工作状态的卫星 $s$（即 $\phi_s(t-1)=1$），它都有可能以一个固定的概率 $p_f$ 发生故障。此过程可以被建模为一个伯努利试验： $\mathbb{P}[\phi_s(t)=0 \mid \phi_s(t-1)=1] = p_f$
2. **区域性失效场景 (Regional Failure Scenario)**：此场景模拟由外部空间环境事件（如太阳耀斑、强磁暴）导致的、具有强空间相关性的集群故障。我们首先定义一个地理区域 $\mathcal{R} \subset \mathcal{S}$，它包含了在时隙 $t$ 位于某一受影响空间范围内的所有卫星。我们假设此类区域性失效事件的发生服从一个泊松过程，其在单个时隙内发生的概率为 $p_{\text{storm}}$。一旦该事件发生，区域 $\mathcal{R}$ 内的所有卫星将同时失效： $\text{若区域性事件在时隙 } t \text{ 发生，则对于所有 } s \in \mathcal{R} \text{，有 } \phi_s(t) = 0$

## **2.7 负载均衡模型 (Load Balancing Model)**

在传统的卫星网络资源调度中，负载均衡通常被定义为一个全局性指标，旨在最小化星座内所有节点间的负载差异。然而，在卫星边缘计算场景下，这种全局均衡策略的有效性值得商榷。其根本原因在于，地面计算任务的生成在空间维度上呈现出显著的非均匀性（non-uniformity）。例如，陆地区域（尤其是城市群）的任务请求密度天然远高于广阔的海洋或极地地区。若强制为了达成全局负载的数学均衡，而将陆地区域产生的任务通过长距离星间链路（ISL）卸载至正飞越海面上空的卫星，不仅会急剧拉长端到端通信路径，还可能引发传输延迟、能耗的显著上升，甚至因多跳中继而增加任务失败的风险。这种“为均衡而均衡”的调度，实际上违背了边缘计算追求低延迟、高能效的初衷。

为解决此问题，我们提出一种更符合物理现实和业务逻辑的**区域负载均衡 (Regional Load Balancing)** 机制。该机制将关注点从不切实际的全局最优，转移到可行的、更具意义的局部区域最优化，旨在实现“区域”内部的资源高效利用，而非全局范围内的强制拉平。

本模型的核心是将全球地表虚拟化为一组离散的、连续的负载感知单元。

1. **区域划分原则**：我们将全球地表沿经纬度划分为 24个固定的空间区域，构成区域集合 $\mathcal{R} = \{R_1, R_2, ..., R_K\}$。这一划分旨在确保每个区域内部的长期任务生成潜力相对同质，同时区域大小与LEO卫星的覆盖足迹相匹配。
    
2. **模型假设**：
    
    在任意时刻 $t$，任何一颗LEO卫星 $s$ 根据其星下点（sub-satellite point）的地理坐标，唯一地从属于一个区域 $R_k \in \mathcal{R}$。
    

首先，定义在时隙 $t$，位于区域 $R_k$ 上空的活跃卫星集合为 $\mathcal{S}_k(t)$。对于该集合中的任意一颗卫星 $s \in \mathcal{S}_k(t)$，其瞬时综合负载 $L_s(t)$ 被建模为其计算资源占用率和任务队列长度的加权和：

$L_s(t) = w_{cpu} \cdot U_{cpu,s}(t) + w_{q} \cdot \frac{N_{queue,s}(t)}{N_{max}}$

其中：

- $U_{cpu,s}(t)$ 是卫星 $s$ 在时隙 $t$ 的CPU利用率。
- $N_{queue,s}(t)$ 是其任务等待队列的当前长度。
- $N_{max}$ 是队列的最大容量，用于归一化。
- $w_{cpu}$ 和 $w_{q}$ 是用于平衡计算负载与排队压力的权重系数。

基于单星负载，我们构建区域 $R_k$ 的**区域负载向量 (Regional Load Vector)**：

$\mathbf{L}_k(t) = [L{s_1}(t), L_{s_2}(t), ..., L_{s_{|\mathcal{S}_k(t)|}}(t)]^T, \quad \forall s_j \in \mathcal{S}_k(t)$

为评估该向量内元素分布的均衡性，我们引入**区域负载方差 (Regional Load Variance)** 作为核心均衡度指标 $\mathcal{B}_k(t)$：

$\mathcal{B}_k(t) = \text{Var}(\mathbf{L}_k(t)) = \frac{1}{|\mathcal{S}k(t)|} \sum{s_{ \in \mathcal{S}_k(t)}} (L_s(t) - \bar{L}_k(t))^2$

其中，$\bar{L}_k(t)$ 是区域 $R_k$ 内所有卫星的平均负载：

$\bar{L}_k(t) = \frac{1}{|\mathcal{S}k(t)|} \sum_{s \in \mathcal{S}_k(t)} L_s(t)$

$\mathcal{B}_k(t)$ 的值越小，表明区域内各卫星间的负载分配越均衡，资源利用率越高。

### **2.7 问题形式化与优化目标 (Problem Formulation and Optimization Objective)**

基于前述对系统架构、任务特性、通信、计算、排队及故障场景的详细建模，本节将对卫星边缘计算环境下的任务卸载与资源调度问题进行统一的数学形式化。问题的核心是在一个资源受限、拓扑动态且存在故障风险的异构网络中，设计一个在线决策策略 $\pi$，以为每一个到达的任务 $T_k$ 分配最优的计算资源（_何处计算_）并确定其处理优先级（_何时计算_），从而在满足任务服务质量（QoS）要求的同时，最小化系统的长期综合运行成本。

### **2.7.1 系统输入与决策变量**

在每个决策时隙 $t$，系统可观测到的状态和参数作为决策的输入：

- **任务集合**：当前时隙 $t$ 新到达的任务集合 $\mathcal{K}(t)$，以及每个任务 $T_k \in \mathcal{K}(t)$ 的完整属性 $\mathcal{T}_k = \{A_k, S_k, C_k, D_k^{\text{max}}, P_k\}$。
- **节点状态**：所有计算节点 $n \in \mathcal{N}$ 的当前状态，包括各LEO卫星的本地任务队列 $\mathcal{Q}_s(t)$ 和剩余能量 $E_s(t)$。
- **网络状态**：所有通信链路的瞬 时容量 $\{R_{ij}(t)\}$，以及由卫星星历决定的节点间距离。
- **故障状态**：整个LEO星座的健康状态向量 $\mathbf{\Phi}(t) = [\phi_1(t), \phi_2(t), ..., \phi_S(t)]$。

为应对上述输入，系统决策策略 $\pi$ 需要在每个时隙为新到达的任务确定以下**决策变量**：

- **卸载决策变量 $x_{k,n}$**：这是一个二元指示变量，用于决定任务 $T_k$ 的计算位置。 $x_{k,n} = \begin{cases} 1, & \text{若任务 } T_k \text{ 被指派到计算节点 } n \in \mathcal{N} \text{ 执行} \\ 0, & \text{其他情况} \end{cases}$ 其中 $\mathcal{N} = \mathcal{S} \cup \{\text{Cloud}\}$ 代表所有可用计算节点的集合。
- **任务丢弃指示变量 $\delta_k$**：这是一个结果变量，也作为隐式决策，反映任务 $T_k$ 的最终命运。 $\delta_k = \begin{cases} 1, & \text{若任务 } T_k \text{ 最终失败（因超时、故障或被主动丢弃）} \\ 0, & \text{若任务 } T_k \text{ 成功完成} \end{cases}$

### **2.7.2 优化目标函数**

我们的目标是最小化系统的长期、综合运行成本，该成本是任务处理延迟、网络总能耗以及任务丢弃惩罚的加权总和。我们寻求一个最优策略 $\pi^*$，该策略将系统状态映射到决策动作，以最小化以下目标函数 $\mathcal{J}$：

$\min_{\pi} \mathcal{J} = \lim_{T_{\text{end}} \to \infty} \frac{1}{T_{\text{end}}} \sum_{t=0}^{T_{\text{end}}-1} \mathbb{E} \left[ w_p \sum_{k \in \mathcal{K}(t)} \delta_k W_k + w_d \sum_{k \in \mathcal{K}(t)} T_k^{\text{total}} + w_e \sum_{k \in \mathcal{K}(t)} E_k^{\text{total}} + w_{lb} \bar{\mathcal{B}}(t) \right]$

其中：

- $\mathbb{E}[\cdot]$ 是对任务随机到达、信道随机变化及节点随机故障等不确定性因素所取的期望。
- $w_d, w_e, w_p$ 是用于平衡延迟、能耗和可靠性三者重要性的非负权重系数，满足 $w_d+w_e+w_p=1$。
- $T_k^{\text{total}}$ 是任务 $T_k$ 的**端到端总延迟**，定义为从任务到达 $A_k$ 到其计算结果返回给用户的总时间。它由一系列通信延迟与计算延迟构成，具体取决于其卸载路径。例如，若任务 $k$ 从用户 $u$ 经卫星 $s$ 卸载至卫星 $m$ 计算，则 $T_k^{\text{total}} = T_{\text{comm}}^{us} + T_{\text{comm}}^{sm} + T_{\text{comp}}^{k,m} + T_{\text{return\_path}}$。
- $E_k^{\text{total}}$ 是为完成任务 $T_k$ 所消耗的**系统总能量**，包括用户端、所有相关卫星的通信能耗和执行节点的计算能耗之和。$E_k^{\text{total}} = E_{\text{comm}, k} + E_{\text{comp}, k}$。
- $\delta_k \cdot W_k$ 是任务 $T_k$ 失败所带来的**惩罚成本**。

### **2.7.3 系统约束条件**

最优策略的求解必须在以下一系列物理和逻辑约束下进行：

1. **任务分配唯一性约束 (C1)**：每个任务最多只能被分配到一个计算节点执行。 $\forall k \in \mathcal{K}(t), \quad \sum_{n \in \mathcal{N}} x_{k,n} \le 1$ 若 $\sum_{n \in \mathcal{N}} x_{k,n} = 0$，则意味着系统决定在接收时就主动丢弃任务 $k$（$\delta_k=1$）。
2. **任务截止时间约束 (C2)**：任何成功完成的任务，其完成时间 $T_k^{\text{finish}}$ 不得超过其绝对截止时间 $D_k^{\text{max}}$。 $\forall k \text{ s.t. } \delta_k=0, \quad T_k^{\text{finish}} \le D_k^{\text{max}}$
3. **计算资源约束 (C3)**：在任意时刻，每个计算节点 $n$ 上正在处理的任务所需计算资源的总和不能超过该节点的总计算能力 $f_n$。此约束由2.5节的排队与调度模型在微观层面保证。
4. **通信链路容量约束 (C4)**：在任意时刻，任何一条通信链路 $(i, j)$ 上的瞬时数据传输速率不能超过该链路的容量 $R_{ij}(t)$。
5. **故障状态约束 (C5)**：任务不能被分配给或路由经过一个处于故障状态的卫星。


# 基于动态效用感知的自适应资源分配模型


本文提出的DURA-RA（Dynamic Utility-aware Adaptive Resource Allocation）模型将传统的串行任务选择问题转化为并行资源分配问题，通过动态效用评估实现计算资源的自适应分配

## 动态效用函数设计


任务$T_i$在时刻$t_{now}$的动态效用函数$U(T_i, t_{now})$由三个加权分量构成：
$$U(T_i, t_{now}) = w_p \cdot f_p(P_i) + w_d \cdot f_d(D_i, t_{now}) + w_{fail} \cdot f_{fail}(W_i)$$
其中静态价值因子$f_p(P_i) = P_i$反映任务的固有重要性。动态紧迫性因子采用对数形式：

$$f_d(D_i, t_{now}) = \log\left(1 + \frac{T_{remain, max}}{D_i - t_{now} + \epsilon}\right)$$

该对数函数相比传统的倒数函数具有更好的数值稳定性，其增长曲线符合边际效用递减原理。$T_{remain, max}$作为归一化常数确保不同时间尺度下的可比性，$\epsilon$防止数值异常。失败惩罚因子$f_{fail}(W_i) = W_i$量化任务失败造成的损失，使效用函数的各分量具有统一的正向贡献特性。

## 基于效用的资源分配机制

  
卫星MEC在任意时刻$t$维护活跃任务集$Q_{active}$，总计算资源$F_{total}$根据各任务的动态效用值进行比例分配：


$$F_i(t) = F_{total} \cdot \frac{U(T_i, t)}{\sum_{j \in Q_{active}} U(T_j, t)}$$

  

该分配机制形成自适应反馈闭环：任务接近截止时间时，其效用值$U_i$增加，自动获得更多计算资源$F_i$，加速处理进程；非紧急任务则维持最小资源分配，避免占用关键资源。这种内生的资源调节机制无需外部干预即可实现动态优化。

  

## 两阶段队列与准入控制

系统采用两级队列架构：等待队列$Q_{wait}$和活跃处理队列$Q_{active}$。新到达任务首先进入$Q_{wait}$，调度器周期性评估其可行性。任务$T_i$的准入条件基于最差情况可行性检查：

$$t_{now} + \frac{C_i}{F_{min\_share}} \leq D_i$$

  

其中$C_i$为任务计算复杂度，$F_{min\_share}$为基于历史数据或理论界限确定的最小资源保证。满足可行性条件的任务进入$Q_{active}$参与资源分配，不满足条件的任务根据系统策略继续等待或直接拒绝。该准入控制机制从源头避免将资源浪费在注定无法完成的任务上，提高系统整体效率。

调度算法的核心在于动态效用计算与资源重分配的连续迭代。新任务到达时被赋予参数$(P_i, D_i, W_i, C_i)$并加入$Q_{wait}$。调度器在每个调度周期评估等待任务的可行性，将通过检查的任务移入$Q_{active}$。对于所有活跃任务，算法实时计算其动态效用值并据此更新资源分配。任务在分配的资源下并行处理，完成后释放的资源在下一调度周期重新分配。这种迭代过程确保资源分配始终与任务的实时优先级和紧迫性保持一致。


