
# satellite.py 项目说明文档

## 项目概览

`satellite.py`是SPACE2卫星边缘计算仿真系统的**单颗卫星任务处理核心模块**，实现了基于动态优先级评分队列（DPSQ）的智能任务调度算法。该模块模拟单颗LEO卫星的完整计算能力，包括任务接收、队列管理、资源分配、跨时隙处理和性能统计。

核心价值在于为卫星边缘计算提供高效的本地任务调度能力。通过DPSQ算法综合考虑任务优先级、截止时间紧迫性和处理成本，实现智能排队调度。支持多任务并行处理（最多200个并发任务），动态CPU资源分配，以及完整的任务生命周期管理（等待→处理→完成/丢弃）。该模块是连接任务分配与实际执行的关键桥梁，为上层调度算法提供真实的卫星计算能力仿真。

## 架构总结

### 模块划分
- **SatelliteTask**: 单个任务的完整数据结构，包含优先级、截止时间、资源分配、跨时隙处理状态等
- **TaskStatus**: 任务状态枚举定义（PENDING/PROCESSING/COMPLETED/DROPPED/FAILED）
- **DPSQScheduler**: DPSQ调度算法实现，动态优先级计算、处理时间估算、可行性检查
- **Satellite**: 卫星主控制器，统一管理任务队列、资源分配、处理执行
- **跨时隙处理模块**: 支持任务在多个时隙间的连续处理，包含进度保存和恢复机制
- **通信集成模块**: 与OrbitalUpdater和CommunicationManager集成，获取真实带宽信息

### 模块协作方式
数据流向：外部任务 → Satellite.add_task() → DPSQScheduler → 资源分配 → 跨时隙处理 → 统计收集
- 外部模块通过`add_task()`接口提交SatelliteTask
- DPSQScheduler计算动态优先级分数并进行可行性检查
- Satellite分配CPU资源给可行的高优先级任务
- 支持任务在多个时隙间连续处理，保持处理进度
- 集成真实的轨道和通信数据进行带宽更新

## 核心流程

1. **任务接收阶段**: 通过`add_task()`接收SatelliteTask，检查队列容量并加入待处理队列
2. **动态调度阶段**: DPSQScheduler计算动态优先级分数：Score = w_p×f_p + w_d×f_d - w_c×f_c
3. **可行性检查**: 估算处理时间，检查是否能在截止时间前完成
4. **资源分配阶段**: 基于任务优先级和紧急度智能分配CPU资源比例
5. **跨时隙处理**: 支持大任务在多个时隙间连续处理，保存和恢复处理进度
6. **带宽更新**: 从CommunicationManager获取真实的卫星-地面通信带宽
7. **状态管理**: 检测任务完成、超时，更新状态并释放资源

关键业务逻辑：DPSQ动态优先级 = w_priority×优先级 + w_urgency×紧迫性 - w_cost×处理成本，其中紧迫性 = 1/(剩余时间+ε)。

## 外部集成

- **与TaskTracking的集成**: 通过`process_task_segment()`接口处理任务分片，返回ProcessingNode信息
- **与OrbitalUpdater的集成**: 获取卫星轨道信息，支持位置相关的处理决策
- **与CommunicationManager的集成**: 通过`update_bandwidth_from_communication()`获取真实通信带宽
- **与config.yaml的集成**: 读取DPSQ权重参数、计算频率、能效系数等配置

## 关键设计决策

- **决策1 - DPSQ三因子模型**: 综合考虑优先级、紧迫性、成本三个维度，比简单优先级调度更适合卫星环境
- **决策2 - 跨时隙处理支持**: 通过processing_progress、remaining_complexity等字段支持大任务的分时处理
- **决策3 - 真实带宽集成**: 集成CommunicationManager获取动态变化的通信带宽，提升仿真真实性
- **决策4 - 智能CPU分配**: 基于任务权重动态分配CPU资源，而非平均分配
- **决策5 - 能耗精确计算**: 基于zeta_leo×CPU_cycles的物理模型计算能耗

## 技术栈

- **主要框架**: Python标准库 + NumPy + PyYAML
- **数学计算**: 基于Shannon容量和链路预算的通信计算
- **核心库**: 
  - `dataclasses`: SatelliteTask数据结构定义
  - `enum`: TaskStatus枚举类型
  - `heapq`: 优先队列实现（虽然导入了但未使用）
  - `logging`: 统一日志记录

## 重要配置

- **f_leo_hz**: 卫星CPU频率（800GHz），直接影响任务处理速度
- **max_parallel_tasks**: 最大并行任务数（200），决定并发处理能力
- **zeta_leo**: 卫星能效系数（1.0e-10 J/cycle），影响能耗计算
- **w_priority/w_urgency/w_cost**: DPSQ权重参数，影响调度决策
- **epsilon_urgency**: 紧迫性计算的平滑因子，避免除零错误

## 学习要点

1. **跨时隙处理设计**: 通过processing_progress、remaining_complexity等字段实现任务状态的跨时隙保持
2. **真实数据集成**: 学会了如何集成轨道和通信模块的真实数据，提升仿真准确性
3. **智能资源分配**: 基于任务权重的CPU分配策略，比平均分配更高效
4. **物理模型应用**: 能耗计算基于真实的物理公式，而非简单估算
5. **缓存优化**: 任务优先级分数缓存机制，避免重复计算
6. **错误处理**: 带宽为零时的graceful处理，避免除零错误
7. **模块解耦**: 通过依赖注入集成外部模块，保持良好的可测试性

## 当前状态与待解决问题

**当前状态**：satellite.py程序**已支持跨时隙任务处理**。通过以下机制实现：
- `processing_progress`: 记录任务处理进度（0-1）
- `remaining_complexity`: 跟踪剩余计算复杂度
- `accumulated_processing_time`: 累计处理时间
- `handle_timeslot_boundary()`: 处理时隙边界状态保存
- `restore_tasks_from_previous_timeslot()`: 恢复未完成任务

**待解决问题**：
1. **任务分割策略优化**: 当前支持任务分片，但分割策略可进一步优化
2. **负载均衡**: 多卫星间的任务负载均衡机制
3. **动态调度参数**: DPSQ权重参数可根据系统状态动态调整
4. **内存管理**: 大规模任务场景下的内存使用优化
````

